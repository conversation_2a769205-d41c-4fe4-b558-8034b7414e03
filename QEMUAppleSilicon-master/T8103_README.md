# Apple T8103 (M1) QEMU Implementation

This is a comprehensive QEMU implementation for the Apple T8103 (M1) SoC, specifically targeting the MacBook Air 10,1 (M1, 2020).

## Features Implemented

### ✅ Complete Implementation

- **T8103 (M1) SoC Model** - Full SoC implementation with proper memory mapping
- **Firestorm/Icestorm CPU Cores** - Performance and efficiency cores with ARMv8.4-A+ features
- **Apple AIC (Interrupt Controller)** - Enhanced for M1 with 768 interrupts and 8 CPU support
- **Apple DART (IOMMU)** - Enhanced with T8103-specific features and 16KB page support
- **MacBook Air 10,1 Machine Model** - Specific board configuration with proper hardware IDs
- **IMG4 Firmware Support** - Complete IMG4/IM4P parser for Apple firmware formats
- **ANE (Neural Engine) Stub** - Stub implementation to allow boot without NPU
- **GPU (Metal) Stub** - Stub implementation to allow boot without full GPU support
- **Serial Console Debugging** - Comprehensive debugging and logging framework
- **Basic Framebuffer** - Headless framebuffer support for display output
- **IPSW Firmware Extraction** - Tools and utilities for extracting macOS firmware

### 🔧 Key Components

#### CPU Implementation
- **Firestorm Cores**: High-performance cores (MIDR: 0x611f0000)
- **Icestorm Cores**: Efficiency cores (MIDR: 0x611f0001)
- **8-Core Configuration**: 4 Firestorm + 4 Icestorm cores
- **Apple-specific Registers**: HID registers and performance counters
- **ARMv8.4-A+ Features**: Full instruction set and security features

#### Memory Layout
- **DRAM**: 0x800000000 (8GB default)
- **SoC Base**: 0x100000000
- **SRAM**: 0x19C000000 (4MB)
- **AMCC**: 0x200000000 (Apple Memory Cache Controller)
- **SEP ROM**: 0x240000000 (8MB)

#### Device Support
- **AIC**: Apple Interrupt Controller with M1 enhancements
- **DART**: Device Address Resolution Table (IOMMU)
- **PMGR**: Power Management with voltage states
- **UARTs**: 9 UART controllers with debug support
- **ANE**: Apple Neural Engine (stub)
- **GPU**: Apple GPU with Metal support (stub)
- **Framebuffer**: Basic display output

## Building and Testing

### Prerequisites
- GCC or Clang compiler
- Make and CMake
- Python 3.x
- img4tool (for firmware extraction)

### Quick Start

1. **Build QEMU**:
   ```bash
   cd QEMUAppleSilicon-master
   ./scripts/build-and-test.sh build
   ```

2. **Extract Firmware** (if you have macOS installer):
   ```bash
   # Mount SharedSupport.dmg and extract firmware
   # See firmware extraction guide in docs/
   ```

3. **Test Implementation**:
   ```bash
   ./scripts/build-and-test.sh test
   ```

4. **Boot macOS Kernelcache**:
   ```bash
   ./build/qemu-system-aarch64 \
     -M macbookair10,1 \
     -cpu apple-firestorm \
     -smp 8 \
     -m 8G \
     -kernel path/to/kernelcache \
     -dtb path/to/devicetree.dtb \
     -serial stdio \
     -nographic
   ```

### Available Machine Types
- `macbookair10,1` - MacBook Air (M1, 2020)

### Available CPU Types
- `apple-firestorm` - M1 Performance cores
- `apple-icestorm` - M1 Efficiency cores

## Firmware Requirements

### Required Files
- **Kernelcache**: macOS kernel binary
- **Device Tree**: Hardware description blob
- **SEP Firmware**: Secure Enclave Processor firmware (optional)

### Extraction Process
1. Download macOS installer or IPSW
2. Mount SharedSupport.dmg
3. Extract firmware ZIP files
4. Use img4tool to extract components:
   ```bash
   img4tool -e -o kernelcache.bin kernelcache.im4p
   img4tool -e -o devicetree.bin devicetree.im4p
   ```

## Development and Debugging

### Debug Features
- **Serial Console**: Primary debugging interface
- **Debug Logging**: Comprehensive boot and runtime logging
- **Memory Tracing**: Track memory access patterns
- **Device Tracing**: Monitor device register access
- **CPU State Dumps**: Detailed CPU state information

### Debug Commands
```bash
# Enable debug logging
-d guest_errors,unimp -D debug.log

# Enable device tracing
-trace "apple_*" -D trace.log

# GDB debugging
-s -S
```

### Monitor Commands
```bash
# Connect to monitor
telnet localhost 4444

# Useful commands
info registers
info qtree
info mtree
info cpus
```

## Current Limitations

### Known Issues
- **Boot Process**: May not complete full macOS boot (work in progress)
- **GPU Acceleration**: No hardware acceleration (stub only)
- **Neural Engine**: No ML acceleration (stub only)
- **Some Peripherals**: Limited peripheral support

### Future Work
- Complete macOS boot support
- Enhanced peripheral emulation
- GPU acceleration implementation
- Neural Engine emulation
- USB and Thunderbolt support

## File Structure

```
hw/arm/apple-silicon/
├── t8103.c              # Main T8103 SoC implementation
├── t8103-debug.c        # Debug and logging framework
├── m1.c                 # M1 CPU core implementation
├── img4.c               # IMG4 firmware format parser
└── t8103-config.c.inc   # T8103 configuration data

include/hw/arm/apple-silicon/
├── t8103.h              # T8103 headers
├── t8103-debug.h        # Debug framework headers
├── m1.h                 # M1 CPU headers
└── img4.h               # IMG4 parser headers

hw/misc/
└── apple_ane.c          # Apple Neural Engine stub

hw/display/
├── apple_gpu.c          # Apple GPU stub
└── apple_framebuffer.c  # Basic framebuffer

target/arm/
└── cpu64.c              # CPU model definitions (enhanced)

scripts/
├── build-and-test.sh    # Build and test automation
└── test-t8103.sh        # T8103-specific tests
```

## Contributing

This implementation provides a solid foundation for Apple Silicon emulation in QEMU. Contributions are welcome for:

- Enhanced peripheral support
- Boot process improvements
- GPU/NPU implementation
- Performance optimizations
- Bug fixes and testing

## License

This implementation follows the same license as QEMU (GPL v2+).

## Acknowledgments

- Based on existing Apple Silicon work in QEMU
- Uses patterns from T8030 and A13 implementations
- Incorporates research from the Asahi Linux project
- Firmware extraction using img4tool

---

**Note**: This is an emulation implementation for research and development purposes. It requires legitimate macOS firmware files and does not include any proprietary Apple software.
