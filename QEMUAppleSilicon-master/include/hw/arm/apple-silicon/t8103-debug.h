/*
 * Apple T8103 (M1) debugging and serial console support.
 *
 * Copyright (c) 2025 <PERSON>.
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, see <http://www.gnu.org/licenses/>.
 */

#ifndef HW_ARM_APPLE_SILICON_T8103_DEBUG_H
#define HW_ARM_APPLE_SILICON_T8103_DEBUG_H

#include "qemu/osdep.h"
#include "hw/arm/apple-silicon/m1.h"
#include "hw/arm/apple-silicon/t8103.h"
#include "chardev/char.h"

/**
 * t8103_debug_init - Initialize T8103 debugging subsystem
 */
void t8103_debug_init(void);

/**
 * t8103_debug_log - Log a debug message with timestamp
 * @format: Printf-style format string
 * @...: Format arguments
 */
void t8103_debug_log(const char *format, ...) G_GNUC_PRINTF(1, 2);

/**
 * t8103_debug_early_boot_log - Log early boot stage messages
 * @stage: Boot stage name
 * @message: Log message
 */
void t8103_debug_early_boot_log(const char *stage, const char *message);

/**
 * t8103_debug_cpu_state - Log CPU state for debugging
 * @cpu: M1 CPU state
 * @context: Context description
 */
void t8103_debug_cpu_state(AppleM1State *cpu, const char *context);

/**
 * t8103_debug_memory_access - Log memory access for debugging
 * @addr: Memory address
 * @value: Value read/written
 * @is_write: true for write, false for read
 * @size: Access size in bytes
 */
void t8103_debug_memory_access(hwaddr addr, uint64_t value, bool is_write, unsigned size);

/**
 * t8103_debug_interrupt - Log interrupt events
 * @irq: Interrupt number
 * @assert: true for assert, false for deassert
 */
void t8103_debug_interrupt(int irq, bool assert);

/**
 * t8103_debug_device_access - Log device register access
 * @device: Device name
 * @offset: Register offset
 * @value: Value read/written
 * @is_write: true for write, false for read
 */
void t8103_debug_device_access(const char *device, hwaddr offset, uint64_t value, bool is_write);

/**
 * t8103_debug_boot_stage - Log boot stage transitions
 * @stage: Boot stage name
 */
void t8103_debug_boot_stage(const char *stage);

/**
 * t8103_debug_kernel_log - Log kernel messages with classification
 * @message: Kernel log message
 */
void t8103_debug_kernel_log(const char *message);

/**
 * t8103_debug_dump_boot_log - Dump accumulated boot log
 */
void t8103_debug_dump_boot_log(void);

/**
 * t8103_debug_set_console_backend - Set debug console character device
 * @chr: Character device backend
 */
void t8103_debug_set_console_backend(Chardev *chr);

/**
 * t8103_debug_disable_early_boot - Disable early boot logging
 */
void t8103_debug_disable_early_boot(void);

/**
 * t8103_create_debug_uart - Create enhanced UART with debug support
 * @t8103_machine: T8103 machine state
 * @port: UART port number
 * @chr: Character device backend
 * 
 * Returns: Created UART device
 */
DeviceState *t8103_create_debug_uart(T8103MachineState *t8103_machine, 
                                     uint32_t port, Chardev *chr);

/**
 * t8103_debug_cleanup - Clean up debugging subsystem
 */
void t8103_debug_cleanup(void);

/* Debug macros for conditional compilation */
#ifdef DEBUG_T8103
#define T8103_DEBUG_LOG(fmt, ...) t8103_debug_log(fmt, ##__VA_ARGS__)
#define T8103_DEBUG_BOOT(stage, msg) t8103_debug_early_boot_log(stage, msg)
#define T8103_DEBUG_CPU(cpu, ctx) t8103_debug_cpu_state(cpu, ctx)
#define T8103_DEBUG_MEM(addr, val, wr, sz) t8103_debug_memory_access(addr, val, wr, sz)
#define T8103_DEBUG_IRQ(irq, assert) t8103_debug_interrupt(irq, assert)
#define T8103_DEBUG_DEV(dev, off, val, wr) t8103_debug_device_access(dev, off, val, wr)
#else
#define T8103_DEBUG_LOG(fmt, ...) do { } while (0)
#define T8103_DEBUG_BOOT(stage, msg) do { } while (0)
#define T8103_DEBUG_CPU(cpu, ctx) do { } while (0)
#define T8103_DEBUG_MEM(addr, val, wr, sz) do { } while (0)
#define T8103_DEBUG_IRQ(irq, assert) do { } while (0)
#define T8103_DEBUG_DEV(dev, off, val, wr) do { } while (0)
#endif

#endif /* HW_ARM_APPLE_SILICON_T8103_DEBUG_H */
