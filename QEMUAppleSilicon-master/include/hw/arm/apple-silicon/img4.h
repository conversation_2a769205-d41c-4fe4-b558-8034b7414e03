/*
 * Apple IMG4 format parser for T8103 (M1) firmware.
 *
 * Copyright (c) 2025 <PERSON>.
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, see <http://www.gnu.org/licenses/>.
 */

#ifndef HW_ARM_APPLE_SILICON_IMG4_H
#define HW_ARM_APPLE_SILICON_IMG4_H

#include "qemu/osdep.h"

/* IMG4 format structures */
typedef struct {
    uint32_t magic;        /* 'IMG4' */
    uint32_t total_length; /* Total length of IMG4 container */
} IMG4Header;

typedef struct {
    uint32_t type;                    /* 4CC type identifier */
    char description[64];             /* Human-readable description */
    const uint8_t *data;             /* Pointer to payload data */
    size_t data_length;              /* Length of payload data */
} IM4PPayload;

/* Common IMG4 payload types for T8103 */
#define IMG4_TYPE_IBSS 0x73736249  /* 'ibss' - iBSS (Boot Security System) */
#define IMG4_TYPE_IBEC 0x63656249  /* 'ibec' - iBEC (Boot Environment Container) */
#define IMG4_TYPE_IBOOT 0x746f6249 /* 'ibot' - iBoot */
#define IMG4_TYPE_LLB  0x626c6c    /* 'llb'  - Low Level Bootloader */
#define IMG4_TYPE_KRNL 0x6c6e726b  /* 'krnl' - Kernel */
#define IMG4_TYPE_DTREE 0x65657274 /* 'tree' - Device Tree */
#define IMG4_TYPE_SEP  0x70657320  /* 'sep ' - SEP firmware */
#define IMG4_TYPE_LOGO 0x6f676f6c  /* 'logo' - Boot logo */
#define IMG4_TYPE_RECM 0x6d636572  /* 'recm' - Recovery mode */

/**
 * img4_parse_header - Parse IMG4 container header
 * @data: IMG4 data buffer
 * @data_size: Size of data buffer
 * @header: Output header structure
 * 
 * Returns: true on success, false on failure
 */
bool img4_parse_header(const uint8_t *data, size_t data_size, IMG4Header *header);

/**
 * img4_parse_im4p - Parse IM4P payload within IMG4 container
 * @data: IM4P data buffer (after IMG4 header)
 * @data_size: Size of data buffer
 * @payload: Output payload structure
 * 
 * Returns: true on success, false on failure
 */
bool img4_parse_im4p(const uint8_t *data, size_t data_size, IM4PPayload *payload);

/**
 * img4_extract_payload - Extract payload data from IMG4 container
 * @img4_data: Complete IMG4 data buffer
 * @img4_size: Size of IMG4 data
 * @payload_data: Output buffer for payload data (caller must free)
 * @payload_size: Output size of payload data
 * 
 * Returns: true on success, false on failure
 */
bool img4_extract_payload(const uint8_t *img4_data, size_t img4_size,
                         uint8_t **payload_data, size_t *payload_size);

/**
 * img4_validate_signature - Validate IMG4 signature (stub implementation)
 * @img4_data: Complete IMG4 data buffer
 * @img4_size: Size of IMG4 data
 * 
 * Returns: true if signature is valid, false otherwise
 */
bool img4_validate_signature(const uint8_t *img4_data, size_t img4_size);

/**
 * img4_get_type_string - Convert 4CC type to string
 * @type: 4CC type identifier
 * 
 * Returns: String representation of type (static buffer)
 */
const char *img4_get_type_string(uint32_t type);

/**
 * img4_is_compressed - Check if payload data is compressed
 * @payload_data: Payload data buffer
 * @payload_size: Size of payload data
 * 
 * Returns: true if data appears to be compressed, false otherwise
 */
bool img4_is_compressed(const uint8_t *payload_data, size_t payload_size);

/**
 * img4_decompress_payload - Decompress payload data (stub implementation)
 * @compressed_data: Compressed payload data
 * @compressed_size: Size of compressed data
 * @decompressed_data: Output buffer for decompressed data (caller must free)
 * @decompressed_size: Output size of decompressed data
 * 
 * Returns: true on success, false on failure
 */
bool img4_decompress_payload(const uint8_t *compressed_data, size_t compressed_size,
                            uint8_t **decompressed_data, size_t *decompressed_size);

#endif /* HW_ARM_APPLE_SILICON_IMG4_H */
