/*
 * Apple IPSW firmware extraction utilities for T8103 (M1).
 *
 * Copyright (c) 2025 <PERSON>.
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, see <http://www.gnu.org/licenses/>.
 */

#ifndef HW_ARM_APPLE_SILICON_IPSW_EXTRACT_H
#define HW_ARM_APPLE_SILICON_IPSW_EXTRACT_H

#include "qemu/osdep.h"
#include "hw/arm/apple-silicon/dtb.h"

/* T8103 firmware component types */
typedef enum {
    T8103_COMPONENT_DTB,        /* Device Tree Blob */
    T8103_COMPONENT_SEP_ROM,    /* SEP ROM firmware */
    T8103_COMPONENT_SEP_FW,     /* SEP application firmware */
    T8103_COMPONENT_KERNELCACHE,/* XNU kernelcache */
    T8103_COMPONENT_NVRAM,      /* NVRAM layout */
    T8103_COMPONENT_BOOTROM,    /* Boot ROM */
    T8103_COMPONENT_LLB,        /* Low Level Bootloader */
    T8103_COMPONENT_IBOOT,      /* iBoot */
    T8103_COMPONENT_MAX
} T8103ComponentType;

/**
 * extract_t8103_firmware_component - Extract a specific firmware component from IPSW
 * @ipsw_path: Path to the IPSW file or extracted contents
 * @component: Type of component to extract
 * @data: Output buffer for component data (caller must free)
 * @size: Output size of component data
 * 
 * Returns: true on success, false on failure
 */
bool extract_t8103_firmware_component(const char *ipsw_path, 
                                      T8103ComponentType component,
                                      uint8_t **data, size_t *size);

/**
 * extract_t8103_device_tree - Extract and parse device tree from IPSW
 * @ipsw_path: Path to the IPSW file or extracted contents
 * 
 * Returns: Parsed device tree root node, or NULL on failure
 */
DTBNode *extract_t8103_device_tree(const char *ipsw_path);

/**
 * extract_t8103_sep_firmware - Extract SEP firmware components
 * @ipsw_path: Path to the IPSW file or extracted contents
 * @sep_rom: Output buffer for SEP ROM (caller must free)
 * @sep_rom_size: Output size of SEP ROM
 * @sep_fw: Output buffer for SEP firmware (caller must free)
 * @sep_fw_size: Output size of SEP firmware
 * 
 * Returns: true on success, false on failure
 */
bool extract_t8103_sep_firmware(const char *ipsw_path, 
                                uint8_t **sep_rom, size_t *sep_rom_size,
                                uint8_t **sep_fw, size_t *sep_fw_size);

/**
 * extract_t8103_kernelcache - Extract XNU kernelcache
 * @ipsw_path: Path to the IPSW file or extracted contents
 * @kernel_data: Output buffer for kernel data (caller must free)
 * @kernel_size: Output size of kernel data
 * 
 * Returns: true on success, false on failure
 */
bool extract_t8103_kernelcache(const char *ipsw_path,
                               uint8_t **kernel_data, size_t *kernel_size);

/**
 * extract_t8103_nvram_layout - Extract NVRAM layout information
 * @ipsw_path: Path to the IPSW file or extracted contents
 * @nvram_data: Output buffer for NVRAM data (caller must free)
 * @nvram_size: Output size of NVRAM data
 * 
 * Returns: true on success, false on failure
 */
bool extract_t8103_nvram_layout(const char *ipsw_path,
                                uint8_t **nvram_data, size_t *nvram_size);

/**
 * validate_t8103_firmware - Validate T8103 firmware components
 * @ipsw_path: Path to the IPSW file or extracted contents
 * 
 * Returns: true if firmware is valid, false otherwise
 */
bool validate_t8103_firmware(const char *ipsw_path);

#endif /* HW_ARM_APPLE_SILICON_IPSW_EXTRACT_H */
