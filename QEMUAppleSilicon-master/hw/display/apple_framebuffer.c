/*
 * Apple T8103 (M1) basic framebuffer for headless boot.
 *
 * Copyright (c) 2025 <PERSON>.
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, see <http://www.gnu.org/licenses/>.
 */

#include "qemu/osdep.h"
#include "hw/sysbus.h"
#include "hw/registerfields.h"
#include "qemu/log.h"
#include "qemu/module.h"
#include "hw/irq.h"
#include "ui/console.h"
#include "ui/pixel_ops.h"
#include "hw/display/framebuffer.h"

#define TYPE_APPLE_FRAMEBUFFER "apple-framebuffer"
OBJECT_DECLARE_SIMPLE_TYPE(AppleFramebufferState, APPLE_FRAMEBUFFER)

/* Framebuffer register offsets */
#define FB_REG_CONTROL      0x0000
#define FB_REG_STATUS       0x0004
#define FB_REG_WIDTH        0x0008
#define FB_REG_HEIGHT       0x000C
#define FB_REG_STRIDE       0x0010
#define FB_REG_FORMAT       0x0014
#define FB_REG_BASE_ADDR    0x0018
#define FB_REG_SIZE         0x001C

/* Framebuffer control bits */
#define FB_CONTROL_ENABLE   (1 << 0)
#define FB_CONTROL_RESET    (1 << 1)
#define FB_CONTROL_VSYNC    (1 << 2)

/* Framebuffer status bits */
#define FB_STATUS_READY     (1 << 0)
#define FB_STATUS_ACTIVE    (1 << 1)
#define FB_STATUS_VSYNC     (1 << 2)

/* Framebuffer formats */
#define FB_FORMAT_ARGB8888  0x0
#define FB_FORMAT_RGB565    0x1
#define FB_FORMAT_XRGB8888  0x2

typedef struct AppleFramebufferState {
    SysBusDevice parent_obj;

    MemoryRegion iomem;
    MemoryRegion fb_mem;
    QemuConsole *con;
    qemu_irq irq;

    /* Framebuffer registers */
    uint32_t control;
    uint32_t status;
    uint32_t width;
    uint32_t height;
    uint32_t stride;
    uint32_t format;
    uint64_t base_addr;
    uint32_t size;

    /* Framebuffer state */
    bool enabled;
    bool invalidate;
    uint8_t *fb_data;
    hwaddr fb_base;
    uint32_t fb_size;
    
    /* Display properties */
    uint32_t default_width;
    uint32_t default_height;
} AppleFramebufferState;

static void apple_framebuffer_update_display(void *opaque)
{
    AppleFramebufferState *s = APPLE_FRAMEBUFFER(opaque);
    DisplaySurface *surface = qemu_console_surface(s->con);
    
    if (!s->enabled || !s->fb_data) {
        return;
    }
    
    if (s->invalidate) {
        /* Simple framebuffer update - copy data to display surface */
        if (surface_width(surface) == s->width &&
            surface_height(surface) == s->height) {
            
            /* Direct copy for matching formats */
            if (s->format == FB_FORMAT_ARGB8888 || s->format == FB_FORMAT_XRGB8888) {
                memcpy(surface_data(surface), s->fb_data, 
                       s->width * s->height * 4);
            } else {
                /* Format conversion would go here */
                memset(surface_data(surface), 0x80, 
                       s->width * s->height * 4);
            }
        }
        
        dpy_gfx_update(s->con, 0, 0, s->width, s->height);
        s->invalidate = false;
    }
}

static void apple_framebuffer_invalidate_display(void *opaque)
{
    AppleFramebufferState *s = APPLE_FRAMEBUFFER(opaque);
    s->invalidate = true;
}

static uint64_t apple_framebuffer_read(void *opaque, hwaddr offset, unsigned size)
{
    AppleFramebufferState *s = APPLE_FRAMEBUFFER(opaque);
    uint64_t result = 0;

    switch (offset) {
    case FB_REG_CONTROL:
        result = s->control;
        break;
    case FB_REG_STATUS:
        result = s->status;
        break;
    case FB_REG_WIDTH:
        result = s->width;
        break;
    case FB_REG_HEIGHT:
        result = s->height;
        break;
    case FB_REG_STRIDE:
        result = s->stride;
        break;
    case FB_REG_FORMAT:
        result = s->format;
        break;
    case FB_REG_BASE_ADDR:
        result = s->base_addr;
        break;
    case FB_REG_SIZE:
        result = s->size;
        break;
    default:
        qemu_log_mask(LOG_GUEST_ERROR,
                      "Framebuffer: read from unknown offset 0x%08x\n", 
                      (uint32_t)offset);
        break;
    }

    return result;
}

static void apple_framebuffer_write(void *opaque, hwaddr offset, uint64_t value, unsigned size)
{
    AppleFramebufferState *s = APPLE_FRAMEBUFFER(opaque);

    switch (offset) {
    case FB_REG_CONTROL:
        s->control = value;
        if (value & FB_CONTROL_ENABLE) {
            s->enabled = true;
            s->status |= FB_STATUS_READY | FB_STATUS_ACTIVE;
            s->invalidate = true;
        } else {
            s->enabled = false;
            s->status &= ~(FB_STATUS_READY | FB_STATUS_ACTIVE);
        }
        if (value & FB_CONTROL_RESET) {
            s->status = 0;
            s->invalidate = true;
        }
        break;
    case FB_REG_WIDTH:
        s->width = value;
        s->stride = s->width * 4; /* Assume 32-bit pixels */
        s->invalidate = true;
        break;
    case FB_REG_HEIGHT:
        s->height = value;
        s->invalidate = true;
        break;
    case FB_REG_STRIDE:
        s->stride = value;
        break;
    case FB_REG_FORMAT:
        s->format = value;
        s->invalidate = true;
        break;
    case FB_REG_BASE_ADDR:
        s->base_addr = value;
        s->fb_base = value;
        /* Map framebuffer memory */
        if (s->fb_data) {
            g_free(s->fb_data);
        }
        s->fb_size = s->width * s->height * 4;
        s->fb_data = g_malloc0(s->fb_size);
        s->invalidate = true;
        break;
    case FB_REG_SIZE:
        s->size = value;
        break;
    default:
        qemu_log_mask(LOG_GUEST_ERROR,
                      "Framebuffer: write to unknown offset 0x%08x\n", 
                      (uint32_t)offset);
        break;
    }
}

static const MemoryRegionOps apple_framebuffer_ops = {
    .read = apple_framebuffer_read,
    .write = apple_framebuffer_write,
    .endianness = DEVICE_NATIVE_ENDIAN,
    .valid = {
        .min_access_size = 4,
        .max_access_size = 8,
    },
};

static const GraphicHwOps apple_framebuffer_gfx_ops = {
    .invalidate = apple_framebuffer_invalidate_display,
    .gfx_update = apple_framebuffer_update_display,
};

static void apple_framebuffer_realize(DeviceState *dev, Error **errp)
{
    AppleFramebufferState *s = APPLE_FRAMEBUFFER(dev);
    SysBusDevice *sbd = SYS_BUS_DEVICE(dev);

    memory_region_init_io(&s->iomem, OBJECT(s), &apple_framebuffer_ops, s,
                          "apple-framebuffer", 0x1000);
    sysbus_init_mmio(sbd, &s->iomem);
    sysbus_init_irq(sbd, &s->irq);

    /* Create console */
    s->con = graphic_console_init(dev, 0, &apple_framebuffer_gfx_ops, s);
    qemu_console_resize(s->con, s->default_width, s->default_height);
}

static void apple_framebuffer_reset(DeviceState *dev)
{
    AppleFramebufferState *s = APPLE_FRAMEBUFFER(dev);

    s->control = 0;
    s->status = 0;
    s->width = s->default_width;
    s->height = s->default_height;
    s->stride = s->width * 4;
    s->format = FB_FORMAT_ARGB8888;
    s->base_addr = 0;
    s->size = 0;
    s->enabled = false;
    s->invalidate = true;
    
    if (s->fb_data) {
        g_free(s->fb_data);
        s->fb_data = NULL;
    }
}

static Property apple_framebuffer_properties[] = {
    DEFINE_PROP_UINT32("width", AppleFramebufferState, default_width, 1920),
    DEFINE_PROP_UINT32("height", AppleFramebufferState, default_height, 1080),
    DEFINE_PROP_END_OF_LIST(),
};

static void apple_framebuffer_class_init(ObjectClass *klass, void *data)
{
    DeviceClass *dc = DEVICE_CLASS(klass);

    dc->realize = apple_framebuffer_realize;
    dc->reset = apple_framebuffer_reset;
    dc->desc = "Apple T8103 basic framebuffer";
    device_class_set_props(dc, apple_framebuffer_properties);
}

static const TypeInfo apple_framebuffer_info = {
    .name = TYPE_APPLE_FRAMEBUFFER,
    .parent = TYPE_SYS_BUS_DEVICE,
    .instance_size = sizeof(AppleFramebufferState),
    .class_init = apple_framebuffer_class_init,
};

static void apple_framebuffer_register_types(void)
{
    type_register_static(&apple_framebuffer_info);
}

type_init(apple_framebuffer_register_types)
