/*
 * Apple GPU (Metal) stub implementation for T8103 (M1).
 *
 * Copyright (c) 2025 <PERSON>.
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, see <http://www.gnu.org/licenses/>.
 */

#include "qemu/osdep.h"
#include "hw/sysbus.h"
#include "hw/registerfields.h"
#include "qemu/log.h"
#include "qemu/module.h"
#include "hw/irq.h"
#include "ui/console.h"

#define TYPE_APPLE_GPU "apple-gpu"
OBJECT_DECLARE_SIMPLE_TYPE(AppleGPUState, APPLE_GPU)

/* GPU register offsets */
#define GPU_REG_VERSION     0x0000
#define GPU_REG_STATUS      0x0004
#define GPU_REG_CONTROL     0x0008
#define GPU_REG_CONFIG      0x000C
#define GPU_REG_POWER       0x0010
#define GPU_REG_INTERRUPT   0x0014
#define GPU_REG_ERROR       0x0018
#define GPU_REG_PERF_CTR    0x001C
#define GPU_REG_MEMORY      0x0020
#define GPU_REG_SHADER      0x0024
#define GPU_REG_RENDER      0x0028
#define GPU_REG_COMPUTE     0x002C

/* GPU status bits */
#define GPU_STATUS_IDLE     (1 << 0)
#define GPU_STATUS_READY    (1 << 1)
#define GPU_STATUS_ERROR    (1 << 2)
#define GPU_STATUS_BUSY     (1 << 3)
#define GPU_STATUS_THERMAL  (1 << 4)

/* GPU control bits */
#define GPU_CONTROL_ENABLE  (1 << 0)
#define GPU_CONTROL_RESET   (1 << 1)
#define GPU_CONTROL_SLEEP   (1 << 2)
#define GPU_CONTROL_WAKE    (1 << 3)

typedef struct AppleGPUState {
    SysBusDevice parent_obj;

    MemoryRegion iomem;
    qemu_irq irq;

    /* GPU registers */
    uint32_t version;
    uint32_t status;
    uint32_t control;
    uint32_t config;
    uint32_t power;
    uint32_t interrupt;
    uint32_t error;
    uint32_t perf_ctr;
    uint32_t memory;
    uint32_t shader;
    uint32_t render;
    uint32_t compute;

    /* GPU state */
    bool enabled;
    bool powered;
    bool sleeping;
    
    /* Framebuffer stub */
    QemuConsole *con;
    uint32_t fb_width;
    uint32_t fb_height;
    uint32_t fb_stride;
    hwaddr fb_base;
} AppleGPUState;

static uint64_t apple_gpu_read(void *opaque, hwaddr offset, unsigned size)
{
    AppleGPUState *s = APPLE_GPU(opaque);
    uint64_t result = 0;

    switch (offset) {
    case GPU_REG_VERSION:
        result = s->version;
        break;
    case GPU_REG_STATUS:
        result = s->status;
        break;
    case GPU_REG_CONTROL:
        result = s->control;
        break;
    case GPU_REG_CONFIG:
        result = s->config;
        break;
    case GPU_REG_POWER:
        result = s->power;
        break;
    case GPU_REG_INTERRUPT:
        result = s->interrupt;
        break;
    case GPU_REG_ERROR:
        result = s->error;
        break;
    case GPU_REG_PERF_CTR:
        result = s->perf_ctr;
        break;
    case GPU_REG_MEMORY:
        result = s->memory;
        break;
    case GPU_REG_SHADER:
        result = s->shader;
        break;
    case GPU_REG_RENDER:
        result = s->render;
        break;
    case GPU_REG_COMPUTE:
        result = s->compute;
        break;
    default:
        qemu_log_mask(LOG_GUEST_ERROR,
                      "GPU: read from unknown offset 0x%08x\n", (uint32_t)offset);
        break;
    }

    qemu_log_mask(LOG_UNIMP, "GPU: read 0x%08x from offset 0x%08x\n",
                  (uint32_t)result, (uint32_t)offset);
    return result;
}

static void apple_gpu_write(void *opaque, hwaddr offset, uint64_t value, unsigned size)
{
    AppleGPUState *s = APPLE_GPU(opaque);

    qemu_log_mask(LOG_UNIMP, "GPU: write 0x%08x to offset 0x%08x\n",
                  (uint32_t)value, (uint32_t)offset);

    switch (offset) {
    case GPU_REG_CONTROL:
        s->control = value;
        if (value & GPU_CONTROL_ENABLE) {
            s->enabled = true;
            s->status |= GPU_STATUS_READY;
            s->status &= ~GPU_STATUS_IDLE;
        } else {
            s->enabled = false;
            s->status |= GPU_STATUS_IDLE;
            s->status &= ~GPU_STATUS_READY;
        }
        if (value & GPU_CONTROL_RESET) {
            /* Reset GPU state */
            s->status = GPU_STATUS_IDLE;
            s->error = 0;
            s->perf_ctr = 0;
        }
        if (value & GPU_CONTROL_SLEEP) {
            s->sleeping = true;
            s->status |= GPU_STATUS_IDLE;
        }
        if (value & GPU_CONTROL_WAKE) {
            s->sleeping = false;
            if (s->powered) {
                s->status |= GPU_STATUS_READY;
            }
        }
        break;
    case GPU_REG_CONFIG:
        s->config = value;
        break;
    case GPU_REG_POWER:
        s->power = value;
        s->powered = (value & 1) != 0;
        if (s->powered && !s->sleeping) {
            s->status |= GPU_STATUS_READY;
        } else {
            s->status &= ~GPU_STATUS_READY;
            s->status |= GPU_STATUS_IDLE;
        }
        break;
    case GPU_REG_INTERRUPT:
        s->interrupt = value;
        break;
    case GPU_REG_MEMORY:
        s->memory = value;
        break;
    case GPU_REG_SHADER:
        s->shader = value;
        break;
    case GPU_REG_RENDER:
        s->render = value;
        break;
    case GPU_REG_COMPUTE:
        s->compute = value;
        break;
    default:
        qemu_log_mask(LOG_GUEST_ERROR,
                      "GPU: write to unknown offset 0x%08x\n", (uint32_t)offset);
        break;
    }
}

static const MemoryRegionOps apple_gpu_ops = {
    .read = apple_gpu_read,
    .write = apple_gpu_write,
    .endianness = DEVICE_NATIVE_ENDIAN,
    .valid = {
        .min_access_size = 4,
        .max_access_size = 4,
    },
};

static void apple_gpu_realize(DeviceState *dev, Error **errp)
{
    AppleGPUState *s = APPLE_GPU(dev);
    SysBusDevice *sbd = SYS_BUS_DEVICE(dev);

    memory_region_init_io(&s->iomem, OBJECT(s), &apple_gpu_ops, s,
                          "apple-gpu", 0x10000);
    sysbus_init_mmio(sbd, &s->iomem);
    sysbus_init_irq(sbd, &s->irq);

    /* Create a stub console for framebuffer */
    s->con = graphic_console_init(dev, 0, NULL, s);
    s->fb_width = 1920;
    s->fb_height = 1080;
    s->fb_stride = s->fb_width * 4; /* 32-bit RGBA */
}

static void apple_gpu_reset(DeviceState *dev)
{
    AppleGPUState *s = APPLE_GPU(dev);

    /* Initialize GPU to a known state */
    s->version = 0x00020001;  /* GPU version 2.1 (M1 GPU) */
    s->status = GPU_STATUS_IDLE;
    s->control = 0;
    s->config = 0;
    s->power = 0;
    s->interrupt = 0;
    s->error = 0;
    s->perf_ctr = 0;
    s->memory = 0;
    s->shader = 0;
    s->render = 0;
    s->compute = 0;
    s->enabled = false;
    s->powered = false;
    s->sleeping = true;
}

static void apple_gpu_class_init(ObjectClass *klass, void *data)
{
    DeviceClass *dc = DEVICE_CLASS(klass);

    dc->realize = apple_gpu_realize;
    dc->reset = apple_gpu_reset;
    dc->desc = "Apple GPU (Metal) stub";
}

static const TypeInfo apple_gpu_info = {
    .name = TYPE_APPLE_GPU,
    .parent = TYPE_SYS_BUS_DEVICE,
    .instance_size = sizeof(AppleGPUState),
    .class_init = apple_gpu_class_init,
};

static void apple_gpu_register_types(void)
{
    type_register_static(&apple_gpu_info);
}

type_init(apple_gpu_register_types)
