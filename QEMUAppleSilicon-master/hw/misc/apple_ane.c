/*
 * Apple Neural Engine (ANE) stub implementation for T8103 (M1).
 *
 * Copyright (c) 2025 <PERSON>.
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, see <http://www.gnu.org/licenses/>.
 */

#include "qemu/osdep.h"
#include "hw/sysbus.h"
#include "hw/registerfields.h"
#include "qemu/log.h"
#include "qemu/module.h"
#include "hw/irq.h"

#define TYPE_APPLE_ANE "apple-ane"
OBJECT_DECLARE_SIMPLE_TYPE(AppleANEState, APPLE_ANE)

/* ANE register offsets */
#define ANE_REG_VERSION     0x0000
#define ANE_REG_STATUS      0x0004
#define ANE_REG_CONTROL     0x0008
#define ANE_REG_CONFIG      0x000C
#define ANE_REG_POWER       0x0010
#define ANE_REG_INTERRUPT   0x0014
#define ANE_REG_ERROR       0x0018
#define ANE_REG_PERF_CTR    0x001C
#define ANE_REG_DEBUG       0x0020

/* ANE status bits */
#define ANE_STATUS_IDLE     (1 << 0)
#define ANE_STATUS_READY    (1 << 1)
#define ANE_STATUS_ERROR    (1 << 2)
#define ANE_STATUS_BUSY     (1 << 3)

/* ANE control bits */
#define ANE_CONTROL_ENABLE  (1 << 0)
#define ANE_CONTROL_RESET   (1 << 1)
#define ANE_CONTROL_START   (1 << 2)
#define ANE_CONTROL_STOP    (1 << 3)

typedef struct AppleANEState {
    SysBusDevice parent_obj;

    MemoryRegion iomem;
    qemu_irq irq;

    /* ANE registers */
    uint32_t version;
    uint32_t status;
    uint32_t control;
    uint32_t config;
    uint32_t power;
    uint32_t interrupt;
    uint32_t error;
    uint32_t perf_ctr;
    uint32_t debug;

    /* ANE state */
    bool enabled;
    bool powered;
} AppleANEState;

static uint64_t apple_ane_read(void *opaque, hwaddr offset, unsigned size)
{
    AppleANEState *s = APPLE_ANE(opaque);
    uint64_t result = 0;

    switch (offset) {
    case ANE_REG_VERSION:
        result = s->version;
        break;
    case ANE_REG_STATUS:
        result = s->status;
        break;
    case ANE_REG_CONTROL:
        result = s->control;
        break;
    case ANE_REG_CONFIG:
        result = s->config;
        break;
    case ANE_REG_POWER:
        result = s->power;
        break;
    case ANE_REG_INTERRUPT:
        result = s->interrupt;
        break;
    case ANE_REG_ERROR:
        result = s->error;
        break;
    case ANE_REG_PERF_CTR:
        result = s->perf_ctr;
        break;
    case ANE_REG_DEBUG:
        result = s->debug;
        break;
    default:
        qemu_log_mask(LOG_GUEST_ERROR,
                      "ANE: read from unknown offset 0x%08x\n", (uint32_t)offset);
        break;
    }

    qemu_log_mask(LOG_UNIMP, "ANE: read 0x%08x from offset 0x%08x\n",
                  (uint32_t)result, (uint32_t)offset);
    return result;
}

static void apple_ane_write(void *opaque, hwaddr offset, uint64_t value, unsigned size)
{
    AppleANEState *s = APPLE_ANE(opaque);

    qemu_log_mask(LOG_UNIMP, "ANE: write 0x%08x to offset 0x%08x\n",
                  (uint32_t)value, (uint32_t)offset);

    switch (offset) {
    case ANE_REG_CONTROL:
        s->control = value;
        if (value & ANE_CONTROL_ENABLE) {
            s->enabled = true;
            s->status |= ANE_STATUS_READY;
            s->status &= ~ANE_STATUS_IDLE;
        } else {
            s->enabled = false;
            s->status |= ANE_STATUS_IDLE;
            s->status &= ~ANE_STATUS_READY;
        }
        if (value & ANE_CONTROL_RESET) {
            /* Reset ANE state */
            s->status = ANE_STATUS_IDLE;
            s->error = 0;
            s->perf_ctr = 0;
        }
        break;
    case ANE_REG_CONFIG:
        s->config = value;
        break;
    case ANE_REG_POWER:
        s->power = value;
        s->powered = (value & 1) != 0;
        if (s->powered) {
            s->status |= ANE_STATUS_READY;
        } else {
            s->status &= ~ANE_STATUS_READY;
            s->status |= ANE_STATUS_IDLE;
        }
        break;
    case ANE_REG_INTERRUPT:
        s->interrupt = value;
        break;
    case ANE_REG_DEBUG:
        s->debug = value;
        break;
    default:
        qemu_log_mask(LOG_GUEST_ERROR,
                      "ANE: write to unknown offset 0x%08x\n", (uint32_t)offset);
        break;
    }
}

static const MemoryRegionOps apple_ane_ops = {
    .read = apple_ane_read,
    .write = apple_ane_write,
    .endianness = DEVICE_NATIVE_ENDIAN,
    .valid = {
        .min_access_size = 4,
        .max_access_size = 4,
    },
};

static void apple_ane_realize(DeviceState *dev, Error **errp)
{
    AppleANEState *s = APPLE_ANE(dev);
    SysBusDevice *sbd = SYS_BUS_DEVICE(dev);

    memory_region_init_io(&s->iomem, OBJECT(s), &apple_ane_ops, s,
                          "apple-ane", 0x1000);
    sysbus_init_mmio(sbd, &s->iomem);
    sysbus_init_irq(sbd, &s->irq);
}

static void apple_ane_reset(DeviceState *dev)
{
    AppleANEState *s = APPLE_ANE(dev);

    /* Initialize ANE to a known state */
    s->version = 0x00010001;  /* ANE version 1.1 */
    s->status = ANE_STATUS_IDLE;
    s->control = 0;
    s->config = 0;
    s->power = 0;
    s->interrupt = 0;
    s->error = 0;
    s->perf_ctr = 0;
    s->debug = 0;
    s->enabled = false;
    s->powered = false;
}

static void apple_ane_class_init(ObjectClass *klass, void *data)
{
    DeviceClass *dc = DEVICE_CLASS(klass);

    dc->realize = apple_ane_realize;
    dc->reset = apple_ane_reset;
    dc->desc = "Apple Neural Engine (ANE) stub";
}

static const TypeInfo apple_ane_info = {
    .name = TYPE_APPLE_ANE,
    .parent = TYPE_SYS_BUS_DEVICE,
    .instance_size = sizeof(AppleANEState),
    .class_init = apple_ane_class_init,
};

static void apple_ane_register_types(void)
{
    type_register_static(&apple_ane_info);
}

type_init(apple_ane_register_types)
