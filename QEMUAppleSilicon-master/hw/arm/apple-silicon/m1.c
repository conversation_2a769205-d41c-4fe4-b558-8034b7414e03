/*
 * Apple M1 CPU (Firestorm/Icestorm cores).
 *
 * Copyright (c) 2025 <PERSON>.
 * Based on A13 implementation by Visual Ehrmanntraut and Christian Inci.
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, see <http://www.gnu.org/licenses/>.
 */

#include "qemu/osdep.h"
#include "qapi/error.h"
#include "qemu/module.h"
#include "qemu/timer.h"
#include "cpu.h"
#include "hw/core/cpu.h"
#include "hw/arm/apple-silicon/dtb.h"
#include "hw/arm/apple-silicon/m1.h"
#include "hw/qdev-properties.h"
#include "hw/registerfields.h"
#include "internals.h"
#include "qemu/log.h"
#include "sysemu/kvm.h"
#include "sysemu/reset.h"
#include "sysemu/tcg.h"
#include "target/arm/cpregs.h"
#include "target/arm/cpu-features.h"
#include "target/arm/gtimer.h"

static QTAILQ_HEAD(, AppleM1Cluster) clusters = QTAILQ_HEAD_INITIALIZER(clusters);

static uint64_t ipi_cr = kDeferredIPITimerDefault;
static QEMUTimer *ipicr_timer = NULL;

inline bool apple_m1_cpu_is_sleep(AppleM1State *tcpu)
{
    return CPU(tcpu)->halted;
}

inline bool apple_m1_cpu_is_powered_off(AppleM1State *tcpu)
{
    return ARM_CPU(tcpu)->power_state == PSCI_OFF;
}

void apple_m1_cpu_start(AppleM1State *tcpu)
{
    int ret = QEMU_ARM_POWERCTL_RET_SUCCESS;

    if (ARM_CPU(tcpu)->power_state != PSCI_ON) {
        ret = arm_set_cpu_on_and_reset(tcpu->mpidr);
    }

    if (ret != QEMU_ARM_POWERCTL_RET_SUCCESS) {
        error_report("Failed to bring up CPU %d: err %d", tcpu->cpu_id, ret);
    }
}

void apple_m1_cpu_reset(AppleM1State *tcpu)
{
    int ret = QEMU_ARM_POWERCTL_RET_SUCCESS;

    if (ARM_CPU(tcpu)->power_state != PSCI_OFF) {
        ret = arm_reset_cpu(tcpu->mpidr);
    }

    if (ret != QEMU_ARM_POWERCTL_RET_SUCCESS) {
        error_report("%s: failed to reset CPU %d: err %d", __func__,
                     tcpu->cpu_id, ret);
    }
}

void apple_m1_cpu_off(AppleM1State *tcpu)
{
    int ret = QEMU_ARM_POWERCTL_RET_SUCCESS;

    if (ARM_CPU(tcpu)->power_state != PSCI_OFF) {
        ret = arm_set_cpu_off(tcpu->mpidr);
    }

    if (ret != QEMU_ARM_POWERCTL_RET_SUCCESS) {
        error_report("%s: failed to turn off CPU %d: err %d", __func__,
                     tcpu->cpu_id, ret);
    }
}

static uint64_t m1_cpreg_read(CPUARMState *env, const ARMCPRegInfo *ri)
{
    AppleM1State *tcpu = APPLE_M1(env_archcpu(env));

    switch (ri->crn) {
    case 15:
        switch (ri->crm) {
        case 0:
            switch (ri->opc2) {
            case 0:
                return tcpu->cpreg_ARM64_REG_HID0;
            case 1:
                return tcpu->cpreg_ARM64_REG_HID1;
            case 3:
                return tcpu->cpreg_ARM64_REG_HID3;
            case 4:
                return tcpu->cpreg_ARM64_REG_HID4;
            case 5:
                return tcpu->cpreg_ARM64_REG_HID5;
            case 7:
                return tcpu->cpreg_ARM64_REG_HID7;
            }
            break;
        case 1:
            switch (ri->opc2) {
            case 0:
                return tcpu->cpreg_ARM64_REG_HID8;
            case 1:
                return tcpu->cpreg_ARM64_REG_HID9;
            case 3:
                return tcpu->cpreg_ARM64_REG_HID11;
            case 5:
                return tcpu->cpreg_ARM64_REG_HID13;
            case 6:
                return tcpu->cpreg_ARM64_REG_HID14;
            }
            break;
        case 2:
            switch (ri->opc2) {
            case 0:
                return tcpu->cpreg_ARM64_REG_HID16;
            }
            break;
        }
        break;
    }

    qemu_log_mask(LOG_UNIMP, "M1 cpreg read: opc0=%d opc1=%d crn=%d crm=%d opc2=%d\n",
                  ri->opc0, ri->opc1, ri->crn, ri->crm, ri->opc2);
    return 0;
}

static void m1_cpreg_write(CPUARMState *env, const ARMCPRegInfo *ri, uint64_t value)
{
    AppleM1State *tcpu = APPLE_M1(env_archcpu(env));

    switch (ri->crn) {
    case 15:
        switch (ri->crm) {
        case 0:
            switch (ri->opc2) {
            case 0:
                tcpu->cpreg_ARM64_REG_HID0 = value;
                return;
            case 1:
                tcpu->cpreg_ARM64_REG_HID1 = value;
                return;
            case 3:
                tcpu->cpreg_ARM64_REG_HID3 = value;
                return;
            case 4:
                tcpu->cpreg_ARM64_REG_HID4 = value;
                return;
            case 5:
                tcpu->cpreg_ARM64_REG_HID5 = value;
                return;
            case 7:
                tcpu->cpreg_ARM64_REG_HID7 = value;
                return;
            }
            break;
        case 1:
            switch (ri->opc2) {
            case 0:
                tcpu->cpreg_ARM64_REG_HID8 = value;
                return;
            case 1:
                tcpu->cpreg_ARM64_REG_HID9 = value;
                return;
            case 3:
                tcpu->cpreg_ARM64_REG_HID11 = value;
                return;
            case 5:
                tcpu->cpreg_ARM64_REG_HID13 = value;
                return;
            case 6:
                tcpu->cpreg_ARM64_REG_HID14 = value;
                return;
            }
            break;
        case 2:
            switch (ri->opc2) {
            case 0:
                tcpu->cpreg_ARM64_REG_HID16 = value;
                return;
            }
            break;
        }
        break;
    }

    qemu_log_mask(LOG_UNIMP, "M1 cpreg write: opc0=%d opc1=%d crn=%d crm=%d opc2=%d value=0x%llx\n",
                  ri->opc0, ri->opc1, ri->crn, ri->crm, ri->opc2, value);
}

static const ARMCPRegInfo m1_cp_reginfo[] = {
    { .name = "HID0", .cp = 15, .crn = 15, .crm = 0, .opc1 = 0, .opc2 = 0,
      .access = PL1_RW, .type = ARM_CP_IO,
      .readfn = m1_cpreg_read, .writefn = m1_cpreg_write },
    { .name = "HID1", .cp = 15, .crn = 15, .crm = 0, .opc1 = 0, .opc2 = 1,
      .access = PL1_RW, .type = ARM_CP_IO,
      .readfn = m1_cpreg_read, .writefn = m1_cpreg_write },
    { .name = "HID3", .cp = 15, .crn = 15, .crm = 0, .opc1 = 0, .opc2 = 3,
      .access = PL1_RW, .type = ARM_CP_IO,
      .readfn = m1_cpreg_read, .writefn = m1_cpreg_write },
    { .name = "HID4", .cp = 15, .crn = 15, .crm = 0, .opc1 = 0, .opc2 = 4,
      .access = PL1_RW, .type = ARM_CP_IO,
      .readfn = m1_cpreg_read, .writefn = m1_cpreg_write },
    { .name = "HID5", .cp = 15, .crn = 15, .crm = 0, .opc1 = 0, .opc2 = 5,
      .access = PL1_RW, .type = ARM_CP_IO,
      .readfn = m1_cpreg_read, .writefn = m1_cpreg_write },
    { .name = "HID7", .cp = 15, .crn = 15, .crm = 0, .opc1 = 0, .opc2 = 7,
      .access = PL1_RW, .type = ARM_CP_IO,
      .readfn = m1_cpreg_read, .writefn = m1_cpreg_write },
    { .name = "HID8", .cp = 15, .crn = 15, .crm = 1, .opc1 = 0, .opc2 = 0,
      .access = PL1_RW, .type = ARM_CP_IO,
      .readfn = m1_cpreg_read, .writefn = m1_cpreg_write },
    { .name = "HID9", .cp = 15, .crn = 15, .crm = 1, .opc1 = 0, .opc2 = 1,
      .access = PL1_RW, .type = ARM_CP_IO,
      .readfn = m1_cpreg_read, .writefn = m1_cpreg_write },
    { .name = "HID11", .cp = 15, .crn = 15, .crm = 1, .opc1 = 0, .opc2 = 3,
      .access = PL1_RW, .type = ARM_CP_IO,
      .readfn = m1_cpreg_read, .writefn = m1_cpreg_write },
    { .name = "HID13", .cp = 15, .crn = 15, .crm = 1, .opc1 = 0, .opc2 = 5,
      .access = PL1_RW, .type = ARM_CP_IO,
      .readfn = m1_cpreg_read, .writefn = m1_cpreg_write },
    { .name = "HID14", .cp = 15, .crn = 15, .crm = 1, .opc1 = 0, .opc2 = 6,
      .access = PL1_RW, .type = ARM_CP_IO,
      .readfn = m1_cpreg_read, .writefn = m1_cpreg_write },
    { .name = "HID16", .cp = 15, .crn = 15, .crm = 2, .opc1 = 0, .opc2 = 0,
      .access = PL1_RW, .type = ARM_CP_IO,
      .readfn = m1_cpreg_read, .writefn = m1_cpreg_write },
    REGINFO_SENTINEL
};

static void apple_m1_initfn(Object *obj)
{
    ARMCPU *cpu = ARM_CPU(obj);
    AppleM1State *tcpu = APPLE_M1(obj);

    /* Set up CPU features for M1 */
    cpu->dtb_compatible = "apple,firestorm";
    cpu->midr = 0x611f0000; /* Apple implementer, Firestorm part number */

    /* Enable ARMv8.4-A features */
    cpu->isar.id_aa64isar0 = FIELD_DP64(cpu->isar.id_aa64isar0, ID_AA64ISAR0, AES, 2);
    cpu->isar.id_aa64isar0 = FIELD_DP64(cpu->isar.id_aa64isar0, ID_AA64ISAR0, SHA1, 1);
    cpu->isar.id_aa64isar0 = FIELD_DP64(cpu->isar.id_aa64isar0, ID_AA64ISAR0, SHA2, 2);
    cpu->isar.id_aa64isar0 = FIELD_DP64(cpu->isar.id_aa64isar0, ID_AA64ISAR0, CRC32, 1);
    cpu->isar.id_aa64isar0 = FIELD_DP64(cpu->isar.id_aa64isar0, ID_AA64ISAR0, ATOMIC, 2);
    cpu->isar.id_aa64isar0 = FIELD_DP64(cpu->isar.id_aa64isar0, ID_AA64ISAR0, RDM, 1);
    cpu->isar.id_aa64isar0 = FIELD_DP64(cpu->isar.id_aa64isar0, ID_AA64ISAR0, SHA3, 1);
    cpu->isar.id_aa64isar0 = FIELD_DP64(cpu->isar.id_aa64isar0, ID_AA64ISAR0, SM3, 1);
    cpu->isar.id_aa64isar0 = FIELD_DP64(cpu->isar.id_aa64isar0, ID_AA64ISAR0, SM4, 1);
    cpu->isar.id_aa64isar0 = FIELD_DP64(cpu->isar.id_aa64isar0, ID_AA64ISAR0, DP, 1);
    cpu->isar.id_aa64isar0 = FIELD_DP64(cpu->isar.id_aa64isar0, ID_AA64ISAR0, FHM, 1);
    cpu->isar.id_aa64isar0 = FIELD_DP64(cpu->isar.id_aa64isar0, ID_AA64ISAR0, TS, 2);

    cpu->isar.id_aa64isar1 = FIELD_DP64(cpu->isar.id_aa64isar1, ID_AA64ISAR1, DPB, 1);
    cpu->isar.id_aa64isar1 = FIELD_DP64(cpu->isar.id_aa64isar1, ID_AA64ISAR1, APA, 1);
    cpu->isar.id_aa64isar1 = FIELD_DP64(cpu->isar.id_aa64isar1, ID_AA64ISAR1, API, 0);
    cpu->isar.id_aa64isar1 = FIELD_DP64(cpu->isar.id_aa64isar1, ID_AA64ISAR1, JSCVT, 1);
    cpu->isar.id_aa64isar1 = FIELD_DP64(cpu->isar.id_aa64isar1, ID_AA64ISAR1, FCMA, 1);
    cpu->isar.id_aa64isar1 = FIELD_DP64(cpu->isar.id_aa64isar1, ID_AA64ISAR1, LRCPC, 2);
    cpu->isar.id_aa64isar1 = FIELD_DP64(cpu->isar.id_aa64isar1, ID_AA64ISAR1, GPA, 1);
    cpu->isar.id_aa64isar1 = FIELD_DP64(cpu->isar.id_aa64isar1, ID_AA64ISAR1, GPI, 0);
    cpu->isar.id_aa64isar1 = FIELD_DP64(cpu->isar.id_aa64isar1, ID_AA64ISAR1, FRINTTS, 1);
    cpu->isar.id_aa64isar1 = FIELD_DP64(cpu->isar.id_aa64isar1, ID_AA64ISAR1, SB, 1);
    cpu->isar.id_aa64isar1 = FIELD_DP64(cpu->isar.id_aa64isar1, ID_AA64ISAR1, SPECRES, 1);

    /* Set counter frequency to 24MHz */
    object_property_set_uint(obj, "cntfrq", 24000000, &error_fatal);

    /* Initialize M1-specific registers */
    tcpu->is_firestorm = true; /* Default to Firestorm, will be set based on cluster type */
}

static void apple_m1_realizefn(DeviceState *dev, Error **errp)
{
    AppleM1State *tcpu = APPLE_M1(dev);
    AppleM1Class *tclass = APPLE_M1_GET_CLASS(dev);
    ARMCPU *cpu = ARM_CPU(dev);

    /* Call parent realize */
    tclass->parent_realize(dev, errp);
    if (*errp) {
        return;
    }

    /* Define M1-specific coprocessor registers */
    define_arm_cp_regs(cpu, m1_cp_reginfo);

    /* Set up memory regions for CPU-specific registers */
    memory_region_init_io(&tcpu->impl_reg, OBJECT(tcpu), NULL, tcpu,
                          "m1-impl-reg", 0x1000);
    memory_region_init_io(&tcpu->coresight_reg, OBJECT(tcpu), NULL, tcpu,
                          "m1-coresight-reg", 0x1000);
}

AppleM1State *apple_m1_cpu_create(DTBNode *node, char *name, uint32_t cpu_id,
                                  uint32_t phys_id, uint32_t cluster_id,
                                  uint8_t cluster_type)
{
    AppleM1State *tcpu;
    ARMCPU *cpu;
    DTBProp *prop;
    Object *obj;

    obj = object_new(TYPE_APPLE_M1);
    tcpu = APPLE_M1(obj);
    cpu = ARM_CPU(obj);

    tcpu->cpu_id = cpu_id;
    tcpu->phys_id = phys_id;
    tcpu->cluster_id = cluster_id;

    /* Set core type based on cluster type */
    if (cluster_type == 'P') {
        /* Performance cluster - Firestorm */
        tcpu->is_firestorm = true;
        cpu->midr = 0x611f0000; /* Firestorm part number */
        cpu->dtb_compatible = "apple,firestorm";
    } else {
        /* Efficiency cluster - Icestorm */
        tcpu->is_firestorm = false;
        cpu->midr = 0x611f0001; /* Icestorm part number */
        cpu->dtb_compatible = "apple,icestorm";
    }

    /* Set MPIDR */
    tcpu->mpidr = tcpu->phys_id | (1LL << 31);
    if (cluster_type == 'P') {
        tcpu->mpidr |= (1 << ARM_AFF2_SHIFT);
    }

    /* Set implementer to Apple */
    cpu->midr = FIELD_DP64(cpu->midr, MIDR_EL1, IMPLEMENTER, 0x61);
    cpu->midr = FIELD_DP64(cpu->midr, MIDR_EL1, VARIANT, 0x1);
    cpu->midr = FIELD_DP64(cpu->midr, MIDR_EL1, REVISION, 0x1);

    /* Set CPU name */
    object_property_add_child(qdev_get_machine(), name, obj);

    /* Realize the CPU */
    qdev_realize(DEVICE(obj), NULL, &error_fatal);

    return tcpu;
}

static void apple_m1_class_init(ObjectClass *klass, void *data)
{
    DeviceClass *dc = DEVICE_CLASS(klass);
    AppleM1Class *tclass = APPLE_M1_CLASS(klass);
    ARMCPUClass *acc = ARM_CPU_CLASS(klass);

    device_class_set_parent_realize(dc, apple_m1_realizefn, &tclass->parent_realize);

    acc->info = &(ARMCPUInfo) {
        .name = "apple-m1",
        .initfn = apple_m1_initfn,
    };
}

static const TypeInfo apple_m1_info = {
    .name = TYPE_APPLE_M1,
    .parent = TYPE_ARM_CPU,
    .instance_size = sizeof(AppleM1State),
    .class_size = sizeof(AppleM1Class),
    .class_init = apple_m1_class_init,
};

/* M1 Cluster implementation */
static void apple_m1_cluster_realize(DeviceState *dev, Error **errp)
{
    AppleM1Cluster *cluster = APPLE_M1_CLUSTER(dev);

    /* Initialize cluster-specific memory region */
    memory_region_init(&cluster->mr, OBJECT(cluster), "m1-cluster", cluster->size);

    /* Add to global cluster list */
    QTAILQ_INSERT_TAIL(&clusters, cluster, next);
}

static void apple_m1_cluster_class_init(ObjectClass *klass, void *data)
{
    DeviceClass *dc = DEVICE_CLASS(klass);

    dc->realize = apple_m1_cluster_realize;
}

static const TypeInfo apple_m1_cluster_info = {
    .name = TYPE_APPLE_M1_CLUSTER,
    .parent = TYPE_CPU_CLUSTER,
    .instance_size = sizeof(AppleM1Cluster),
    .class_init = apple_m1_cluster_class_init,
};

static void apple_m1_register_types(void)
{
    type_register_static(&apple_m1_info);
    type_register_static(&apple_m1_cluster_info);
}

type_init(apple_m1_register_types);
