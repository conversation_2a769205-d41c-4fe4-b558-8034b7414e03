/*
 * Apple T8103 (M1) debugging and serial console support.
 *
 * Copyright (c) 2025 <PERSON>.
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, see <http://www.gnu.org/licenses/>.
 */

#include "qemu/osdep.h"
#include "qemu/log.h"
#include "qemu/error-report.h"
#include "hw/arm/apple-silicon/t8103.h"
#include "hw/arm/apple-silicon/dtb.h"
#include "hw/char/apple_uart.h"
#include "chardev/char-fe.h"
#include "sysemu/sysemu.h"

/* Debug UART configuration for T8103 */
#define T8103_DEBUG_UART_BASE   0x235200000
#define T8103_DEBUG_UART_SIZE   0x4000
#define T8103_DEBUG_UART_IRQ    605

/* Boot log buffer for early debugging */
#define BOOT_LOG_SIZE           (64 * 1024)
static char boot_log_buffer[BOOT_LOG_SIZE];
static size_t boot_log_pos = 0;
static bool boot_log_enabled = true;

/* Debug console state */
typedef struct {
    CharBackend chr;
    bool enabled;
    bool early_boot;
    FILE *log_file;
} T8103DebugConsole;

static T8103DebugConsole debug_console = { 0 };

void t8103_debug_init(void)
{
    /* Initialize debug console */
    debug_console.enabled = true;
    debug_console.early_boot = true;
    
    /* Open debug log file */
    debug_console.log_file = fopen("t8103_debug.log", "w");
    if (!debug_console.log_file) {
        warn_report("Failed to open debug log file");
    }
    
    qemu_log_mask(LOG_GUEST_ERROR, "T8103 Debug: Initialized debug console\n");
}

void t8103_debug_log(const char *format, ...)
{
    va_list args;
    char buffer[1024];
    int len;
    
    va_start(args, format);
    len = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    if (len <= 0) {
        return;
    }
    
    /* Add to boot log buffer if in early boot */
    if (boot_log_enabled && boot_log_pos + len < BOOT_LOG_SIZE - 1) {
        memcpy(boot_log_buffer + boot_log_pos, buffer, len);
        boot_log_pos += len;
        boot_log_buffer[boot_log_pos] = '\0';
    }
    
    /* Write to debug log file */
    if (debug_console.log_file) {
        fwrite(buffer, 1, len, debug_console.log_file);
        fflush(debug_console.log_file);
    }
    
    /* Write to QEMU log */
    qemu_log_mask(LOG_GUEST_ERROR, "T8103: %s", buffer);
    
    /* Write to console if available */
    if (debug_console.enabled && qemu_chr_fe_backend_connected(&debug_console.chr)) {
        qemu_chr_fe_write_all(&debug_console.chr, (uint8_t *)buffer, len);
    }
}

void t8103_debug_early_boot_log(const char *stage, const char *message)
{
    struct timeval tv;
    gettimeofday(&tv, NULL);
    
    t8103_debug_log("[%ld.%06ld] BOOT %s: %s\n", 
                   tv.tv_sec, tv.tv_usec, stage, message);
}

void t8103_debug_cpu_state(AppleM1State *cpu, const char *context)
{
    CPUState *cs = CPU(cpu);
    CPUARMState *env = &ARM_CPU(cpu)->env;
    
    t8103_debug_log("CPU%d %s: PC=0x%016lx SP=0x%016lx PSTATE=0x%08x\n",
                   cpu->cpu_id, context,
                   env->pc, env->xregs[31], pstate_read(env));
    
    if (cs->exception_index != EXCP_NONE) {
        t8103_debug_log("CPU%d Exception: %d\n", cpu->cpu_id, cs->exception_index);
    }
}

void t8103_debug_memory_access(hwaddr addr, uint64_t value, bool is_write, unsigned size)
{
    const char *region = "UNKNOWN";
    
    /* Identify memory region */
    if (addr >= 0x800000000 && addr < 0x1000000000) {
        region = "DRAM";
    } else if (addr >= 0x100000000 && addr < 0x200000000) {
        region = "SOC";
    } else if (addr >= 0x200000000 && addr < 0x300000000) {
        region = "AMCC";
    } else if (addr >= 0x19C000000 && addr < 0x1A0000000) {
        region = "SRAM";
    }
    
    t8103_debug_log("MEM %s: %s 0x%016lx = 0x%016lx (size=%d)\n",
                   region, is_write ? "WRITE" : "READ", addr, value, size);
}

void t8103_debug_interrupt(int irq, bool assert)
{
    t8103_debug_log("IRQ: %s IRQ %d\n", assert ? "ASSERT" : "DEASSERT", irq);
}

void t8103_debug_device_access(const char *device, hwaddr offset, uint64_t value, bool is_write)
{
    t8103_debug_log("DEV %s: %s offset=0x%08lx value=0x%016lx\n",
                   device, is_write ? "WRITE" : "READ", offset, value);
}

void t8103_debug_boot_stage(const char *stage)
{
    static const char *last_stage = NULL;
    
    if (last_stage && strcmp(last_stage, stage) == 0) {
        return; /* Avoid duplicate messages */
    }
    
    t8103_debug_early_boot_log("STAGE", stage);
    last_stage = stage;
}

void t8103_debug_kernel_log(const char *message)
{
    /* Parse kernel log messages and add debug info */
    if (strstr(message, "panic") || strstr(message, "Panic")) {
        t8103_debug_log("KERNEL PANIC: %s\n", message);
    } else if (strstr(message, "error") || strstr(message, "Error")) {
        t8103_debug_log("KERNEL ERROR: %s\n", message);
    } else if (strstr(message, "warning") || strstr(message, "Warning")) {
        t8103_debug_log("KERNEL WARN: %s\n", message);
    } else {
        t8103_debug_log("KERNEL: %s\n", message);
    }
}

void t8103_debug_dump_boot_log(void)
{
    if (boot_log_pos > 0) {
        t8103_debug_log("=== BOOT LOG DUMP ===\n");
        t8103_debug_log("%s", boot_log_buffer);
        t8103_debug_log("=== END BOOT LOG ===\n");
    }
}

void t8103_debug_set_console_backend(Chardev *chr)
{
    if (chr) {
        qemu_chr_fe_init(&debug_console.chr, chr, &error_abort);
        debug_console.enabled = true;
        t8103_debug_log("Debug console connected\n");
    }
}

void t8103_debug_disable_early_boot(void)
{
    debug_console.early_boot = false;
    boot_log_enabled = false;
    t8103_debug_log("Early boot debugging disabled\n");
}

/* Enhanced UART creation with debug support */
DeviceState *t8103_create_debug_uart(T8103MachineState *t8103_machine, 
                                     uint32_t port, Chardev *chr)
{
    DeviceState *dev;
    DTBNode *child;
    DTBProp *prop;
    hwaddr *uart_offset;
    hwaddr base;
    uint32_t *ints;

    child = dtb_get_node(t8103_machine->device_tree, "arm-io/uart0");
    if (!child) {
        /* Create UART node if it doesn't exist */
        DTBNode *arm_io = dtb_get_node(t8103_machine->device_tree, "arm-io");
        child = dtb_new_node("uart0");
        dtb_set_prop_str(child, "compatible", "apple,t8103-uart");
        dtb_set_prop_str(child, "device_type", "serial");
        
        uint64_t uart_reg[] = { 0x235200000, 0x4000 };
        dtb_set_prop(child, "reg", sizeof(uart_reg), uart_reg);
        
        uint32_t uart_interrupts[] = { 0, 605, 4 };
        dtb_set_prop(child, "interrupts", sizeof(uart_interrupts), uart_interrupts);
        
        dtb_add_child(arm_io, child);
    }

    prop = dtb_find_prop(child, "reg");
    g_assert_nonnull(prop);

    uart_offset = (hwaddr *)prop->data;
    base = t8103_machine->soc_base_pa + uart_offset[0] + uart_offset[1] * port;

    prop = dtb_find_prop(child, "interrupts");
    g_assert_nonnull(prop);

    dev = apple_uart_create(base, 15, 0, chr,
                           qdev_get_gpio_in(DEVICE(t8103_machine->aic),
                                           ((uint32_t *)prop->data)[0] + port));
    
    /* Set up debug console if this is the primary UART */
    if (port == 0 && chr) {
        t8103_debug_set_console_backend(chr);
    }
    
    object_property_add_child(OBJECT(t8103_machine), "debug-uart", OBJECT(dev));
    
    t8103_debug_log("Created debug UART at 0x%016lx\n", base);
    
    return dev;
}

void t8103_debug_cleanup(void)
{
    if (debug_console.log_file) {
        fclose(debug_console.log_file);
        debug_console.log_file = NULL;
    }
    
    if (qemu_chr_fe_backend_connected(&debug_console.chr)) {
        qemu_chr_fe_deinit(&debug_console.chr, false);
    }
}
