/* T8103 (M1) SoC Configuration Data */

/* Voltage states for M1 power management */
static uint8_t t8103_voltage_states5[] = {
    0x12, 0xDA, 0x01, 0x00, 0x45, 0x02, 0x00, 0x00, 0xB3, 0x18, 0x01,
    0x00, 0x7A, 0x02, 0x00, 0x00, 0x87, 0xC5, 0x00, 0x00, 0xB2, 0x02,
    0x00, 0x00, 0xA2, 0x89, 0x00, 0x00, 0x19, 0x03, 0x00, 0x00, 0x37,
    0x75, 0x00, 0x00, 0x74, 0x03, 0x00, 0x00, 0xAA, 0x6A, 0x00, 0x00,
    0xC8, 0x03, 0x00, 0x00, 0xC3, 0x62, 0x00, 0x00, 0x29, 0x04, 0x00,
    0x00, 0x18, 0x60, 0x00, 0x00, 0x3F, 0x04, 0x00, 0x00,
};

static uint8_t t8103_voltage_states9_sram[] = {
    0x00, 0x00, 0x00, 0x00, 0xF1, 0x02, 0x00, 0x00, 0x00, 0x2A, 0x75, 0x15,
    0xF1, 0x02, 0x00, 0x00, 0xC0, 0x4F, 0xEF, 0x1E, 0xF1, 0x02, 0x00, 0x00,
    0x00, 0xCD, 0x56, 0x27, 0xF1, 0x02, 0x00, 0x00, 0x00, 0x11, 0xEC, 0x2F,
    0xF1, 0x02, 0x00, 0x00, 0x00, 0x55, 0x81, 0x38, 0x19, 0x03, 0x00, 0x00,
    0x80, 0xFE, 0x2A, 0x47, 0x84, 0x03, 0x00, 0x00
};

static uint8_t t8103_voltage_states0[] = {
    0x01, 0x00, 0x00, 0x00, 0x77, 0x02, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
    0xB8, 0x02, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0xEE, 0x02, 0x00, 0x00,
};

static uint8_t t8103_voltage_states9[] = {
    0x00, 0x00, 0x00, 0x00, 0x90, 0x01, 0x00, 0x00, 0x00, 0x2A, 0x75, 0x15,
    0x48, 0x02, 0x00, 0x00, 0xC0, 0x4F, 0xEF, 0x1E, 0x77, 0x02, 0x00, 0x00,
    0x00, 0xCD, 0x56, 0x27, 0x99, 0x02, 0x00, 0x00, 0x00, 0x11, 0xEC, 0x2F,
    0xD1, 0x02, 0x00, 0x00, 0x00, 0x55, 0x81, 0x38, 0x19, 0x03, 0x00, 0x00,
    0x80, 0xFE, 0x2A, 0x47, 0x84, 0x03, 0x00, 0x00,
};

static uint8_t t8103_voltage_states2[] = {
    0x01, 0x00, 0x00, 0x00, 0x74, 0x02, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
    0xB5, 0x02, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x2C, 0x03, 0x00, 0x00,
};

static uint8_t t8103_voltage_states1_sram[] = {
    0x00, 0x10, 0x55, 0x22, 0xF1, 0x02, 0x00, 0x00, 0x00, 0x98,
    0x7F, 0x33, 0xF1, 0x02, 0x00, 0x00, 0x00, 0x20, 0xAA, 0x44,
    0xF1, 0x02, 0x00, 0x00, 0x00, 0xA8, 0xD4, 0x55, 0x32, 0x03,
    0x00, 0x00, 0x00, 0x30, 0xFF, 0x66, 0x99, 0x03, 0x00, 0x00,
};

static uint8_t t8103_voltage_states10[] = {
    0x01, 0x00, 0x00, 0x00, 0x7A, 0x02, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
    0xA3, 0x02, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0xBF, 0x02, 0x00, 0x00,
};

static uint8_t t8103_voltage_states11[] = {
    0x01, 0x00, 0x00, 0x00, 0x45, 0x02, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
    0x7D, 0x02, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0xF4, 0x02, 0x00, 0x00,
};

static uint8_t t8103_voltage_states8[] = {
    0x00, 0xF4, 0x06, 0x14, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x2A, 0x75, 0x15,
    0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x6E, 0x0A, 0x1E, 0xFF, 0xFF, 0xFF, 0xFF,
    0x00, 0xBF, 0x2F, 0x20, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x1E, 0x7C, 0x29,
    0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0xA5, 0x0F, 0x2D, 0xFF, 0xFF, 0xFF, 0xFF,
    0x00, 0x55, 0x81, 0x38, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x7E, 0x5F, 0x40,
    0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0xB4, 0xCD, 0x41, 0xFF, 0xFF, 0xFF, 0xFF,
    0x00, 0x8C, 0x86, 0x47, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x64, 0x3F, 0x4D,
    0xFF, 0xFF, 0xFF, 0xFF, 0x80, 0xC9, 0x53, 0x53, 0xFF, 0xFF, 0xFF, 0xFF,
};

static uint8_t t8103_voltage_states5_sram[] = {
    0x00, 0xBF, 0x2F, 0x20, 0xF1, 0x02, 0x00, 0x00, 0x00, 0x04, 0x5C,
    0x36, 0xF1, 0x02, 0x00, 0x00, 0x00, 0x64, 0x3F, 0x4D, 0xF1, 0x02,
    0x00, 0x00, 0x00, 0x59, 0xDD, 0x6E, 0x19, 0x03, 0x00, 0x00, 0x00,
    0x32, 0x2D, 0x82, 0x74, 0x03, 0x00, 0x00, 0x00, 0x18, 0x0D, 0x8F,
    0xC8, 0x03, 0x00, 0x00, 0x00, 0xC8, 0x7E, 0x9A, 0x29, 0x04, 0x00,
    0x00, 0x00, 0x6A, 0xC9, 0x9E, 0x3F, 0x04, 0x00, 0x00,
};

static uint8_t t8103_voltage_states1[] = {
    0x71, 0xBC, 0x01, 0x00, 0x61, 0x02, 0x00, 0x00, 0x4B, 0x28,
    0x01, 0x00, 0x99, 0x02, 0x00, 0x00, 0x38, 0xDE, 0x00, 0x00,
    0xDE, 0x02, 0x00, 0x00, 0xC7, 0xB1, 0x00, 0x00, 0x32, 0x03,
    0x00, 0x00, 0x25, 0x94, 0x00, 0x00, 0x99, 0x03, 0x00, 0x00,
};

/* Bridge settings for M1 interconnect */
static uint8_t t8103_bridge_settings17[] = {
    0x00, 0x00, 0x00, 0x00, 0x0F, 0x00, 0x00, 0x00,
    0x00, 0x02, 0x00, 0x00, 0x01, 0x00, 0x01, 0xC0,
};

static uint8_t t8103_bridge_settings15[] = {
    0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00,
    0x98, 0x7E, 0x68, 0x01, 0x00, 0x0A, 0x00, 0x00, 0x01, 0x00, 0x01, 0x40,
    0x24, 0x0A, 0x00, 0x00, 0x18, 0x08, 0x08, 0x00, 0x44, 0x0A, 0x00, 0x00,
    0x18, 0x08, 0x08, 0x00, 0x64, 0x0A, 0x00, 0x00, 0x18, 0x08, 0x08, 0x00,
    0x84, 0x0A, 0x00, 0x00, 0x18, 0x08, 0x08, 0x00, 0x00, 0x0B, 0x00, 0x00,
    0x7F, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x01, 0x01, 0x00, 0x00,
    0x00, 0x40, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x04, 0x40, 0x00, 0x00,
    0x03, 0x00, 0x00, 0x00, 0x08, 0x40, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
    0x0C, 0x40, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x10, 0x40, 0x00, 0x00,
    0x03, 0x00, 0x00, 0x00, 0x04, 0x41, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
    0x00, 0x43, 0x00, 0x00, 0x01, 0x00, 0x01, 0xC0, 0x00, 0x80, 0x00, 0x00,
    0x0F, 0x00, 0x00, 0x00, 0x00, 0x82, 0x00, 0x00, 0x01, 0x00, 0x01, 0xC0,
};

static uint8_t t8103_bridge_settings13[] = {
    0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x0C, 0x00, 0x00, 0x00,
    0x98, 0x7E, 0x68, 0x01, 0x00, 0x0A, 0x00, 0x00, 0x01, 0x00, 0x01, 0x40,
    0x24, 0x0A, 0x00, 0x00, 0x18, 0x08, 0x08, 0x00, 0x44, 0x0A, 0x00, 0x00,
    0x18, 0x08, 0x08, 0x00, 0x64, 0x0A, 0x00, 0x00, 0x18, 0x08, 0x08, 0x00,
    0x84, 0x0A, 0x00, 0x00, 0x18, 0x08, 0x08, 0x00, 0x00, 0x0B, 0x00, 0x00,
    0x7F, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x01, 0x01, 0x00, 0x00,
    0x00, 0x40, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x04, 0x40, 0x00, 0x00,
    0x03, 0x00, 0x00, 0x00, 0x08, 0x40, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00,
    0x0C, 0x40, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x10, 0x40, 0x00, 0x00,
    0x03, 0x00, 0x00, 0x00, 0x04, 0x41, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00,
    0x00, 0x43, 0x00, 0x00, 0x01, 0x00, 0x01, 0xC0, 0x00, 0x80, 0x00, 0x00,
    0x0F, 0x00, 0x00, 0x00, 0x00, 0x82, 0x00, 0x00, 0x01, 0x00, 0x01, 0xC0,
};

/* M1 CPU cluster configuration */
static uint8_t t8103_cpu_cluster_config[] = {
    0x08, 0x00, 0x00, 0x00, /* 8 CPU cores total */
    0x04, 0x00, 0x00, 0x00, /* 4 Firestorm (performance) cores */
    0x04, 0x00, 0x00, 0x00, /* 4 Icestorm (efficiency) cores */
    0x02, 0x00, 0x00, 0x00, /* 2 clusters */
};

/* M1 memory configuration */
static uint8_t t8103_memory_config[] = {
    0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x80, /* DRAM base: 0x800000000 */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, /* DRAM size: 8GB default */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, /* SRAM base: 0x100000000 */
    0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, /* SRAM size: 8MB */
};

/* M1 GPU configuration (stub) */
static uint8_t t8103_gpu_config[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, /* GPU disabled for now */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};
