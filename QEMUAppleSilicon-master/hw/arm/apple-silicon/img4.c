/*
 * Apple IMG4 format parser for T8103 (M1) firmware.
 *
 * Copyright (c) 2025 <PERSON>.
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, see <http://www.gnu.org/licenses/>.
 */

#include "qemu/osdep.h"
#include "qemu/error-report.h"
#include "qemu/log.h"
#include "hw/arm/apple-silicon/img4.h"

/* IMG4 magic numbers */
#define IMG4_MAGIC 0x34474d49  /* 'IMG4' */
#define IM4P_MAGIC 0x50344d49  /* 'IM4P' */
#define IM4M_MAGIC 0x4d344d49  /* 'IM4M' */
#define IM4R_MAGIC 0x52344d49  /* 'IM4R' */

/* ASN.1 DER tags */
#define ASN1_SEQUENCE 0x30
#define ASN1_IA5STRING 0x16
#define ASN1_OCTET_STRING 0x04
#define ASN1_INTEGER 0x02

static uint32_t read_be32(const uint8_t *data)
{
    return (data[0] << 24) | (data[1] << 16) | (data[2] << 8) | data[3];
}

static uint64_t read_be64(const uint8_t *data)
{
    return ((uint64_t)read_be32(data) << 32) | read_be32(data + 4);
}

static size_t parse_asn1_length(const uint8_t *data, size_t *length_bytes)
{
    if (data[0] & 0x80) {
        /* Long form */
        size_t num_bytes = data[0] & 0x7f;
        size_t length = 0;
        
        if (num_bytes > 8) {
            return 0; /* Invalid length */
        }
        
        for (size_t i = 0; i < num_bytes; i++) {
            length = (length << 8) | data[1 + i];
        }
        
        *length_bytes = 1 + num_bytes;
        return length;
    } else {
        /* Short form */
        *length_bytes = 1;
        return data[0];
    }
}

static bool parse_asn1_tag(const uint8_t *data, size_t data_size,
                          uint8_t expected_tag, size_t *content_offset,
                          size_t *content_length)
{
    if (data_size < 2) {
        return false;
    }
    
    if (data[0] != expected_tag) {
        return false;
    }
    
    size_t length_bytes;
    size_t length = parse_asn1_length(data + 1, &length_bytes);
    
    if (length == 0 || 1 + length_bytes + length > data_size) {
        return false;
    }
    
    *content_offset = 1 + length_bytes;
    *content_length = length;
    return true;
}

bool img4_parse_header(const uint8_t *data, size_t data_size, IMG4Header *header)
{
    if (!data || !header || data_size < sizeof(IMG4Header)) {
        return false;
    }
    
    /* Check IMG4 magic */
    if (read_be32(data) != IMG4_MAGIC) {
        return false;
    }
    
    header->magic = IMG4_MAGIC;
    header->total_length = read_be32(data + 4);
    
    /* Validate total length */
    if (header->total_length > data_size) {
        return false;
    }
    
    return true;
}

bool img4_parse_im4p(const uint8_t *data, size_t data_size, IM4PPayload *payload)
{
    if (!data || !payload || data_size < 16) {
        return false;
    }
    
    size_t offset = 0;
    size_t content_offset, content_length;
    
    /* Parse outer SEQUENCE */
    if (!parse_asn1_tag(data + offset, data_size - offset, ASN1_SEQUENCE,
                       &content_offset, &content_length)) {
        return false;
    }
    offset += content_offset;
    
    /* Parse IM4P magic */
    if (!parse_asn1_tag(data + offset, data_size - offset, ASN1_IA5STRING,
                       &content_offset, &content_length)) {
        return false;
    }
    
    if (content_length != 4 || 
        read_be32(data + offset + content_offset) != IM4P_MAGIC) {
        return false;
    }
    offset += content_offset + content_length;
    
    /* Parse type (4CC) */
    if (!parse_asn1_tag(data + offset, data_size - offset, ASN1_IA5STRING,
                       &content_offset, &content_length)) {
        return false;
    }
    
    if (content_length != 4) {
        return false;
    }
    
    payload->type = read_be32(data + offset + content_offset);
    offset += content_offset + content_length;
    
    /* Parse description */
    if (!parse_asn1_tag(data + offset, data_size - offset, ASN1_IA5STRING,
                       &content_offset, &content_length)) {
        return false;
    }
    
    if (content_length >= sizeof(payload->description)) {
        return false;
    }
    
    memcpy(payload->description, data + offset + content_offset, content_length);
    payload->description[content_length] = '\0';
    offset += content_offset + content_length;
    
    /* Parse payload data */
    if (!parse_asn1_tag(data + offset, data_size - offset, ASN1_OCTET_STRING,
                       &content_offset, &content_length)) {
        return false;
    }
    
    payload->data = data + offset + content_offset;
    payload->data_length = content_length;
    
    return true;
}

bool img4_extract_payload(const uint8_t *img4_data, size_t img4_size,
                         uint8_t **payload_data, size_t *payload_size)
{
    IMG4Header header;
    IM4PPayload payload;
    
    if (!img4_parse_header(img4_data, img4_size, &header)) {
        return false;
    }
    
    /* Skip IMG4 header and find IM4P */
    size_t offset = 8; /* Skip IMG4 magic and length */
    
    if (!img4_parse_im4p(img4_data + offset, img4_size - offset, &payload)) {
        return false;
    }
    
    /* Allocate and copy payload data */
    *payload_data = g_malloc(payload.data_length);
    memcpy(*payload_data, payload.data, payload.data_length);
    *payload_size = payload.data_length;
    
    return true;
}

bool img4_validate_signature(const uint8_t *img4_data, size_t img4_size)
{
    /* TODO: Implement actual signature validation */
    /* For now, just check if the IMG4 structure is valid */
    IMG4Header header;
    return img4_parse_header(img4_data, img4_size, &header);
}

const char *img4_get_type_string(uint32_t type)
{
    static char type_str[5];
    
    type_str[0] = (type >> 24) & 0xff;
    type_str[1] = (type >> 16) & 0xff;
    type_str[2] = (type >> 8) & 0xff;
    type_str[3] = type & 0xff;
    type_str[4] = '\0';
    
    return type_str;
}

bool img4_is_compressed(const uint8_t *payload_data, size_t payload_size)
{
    if (payload_size < 4) {
        return false;
    }
    
    /* Check for common compression signatures */
    uint32_t magic = read_be32(payload_data);
    
    /* LZFSE */
    if (magic == 0x62767832 || magic == 0x62767831) { /* 'bvx2' or 'bvx1' */
        return true;
    }
    
    /* LZMA */
    if (payload_data[0] == 0x5d && payload_data[1] == 0x00) {
        return true;
    }
    
    /* Gzip */
    if (payload_data[0] == 0x1f && payload_data[1] == 0x8b) {
        return true;
    }
    
    return false;
}

bool img4_decompress_payload(const uint8_t *compressed_data, size_t compressed_size,
                            uint8_t **decompressed_data, size_t *decompressed_size)
{
    /* TODO: Implement decompression for LZFSE, LZMA, etc. */
    /* For now, just copy the data if it's not compressed */
    
    if (!img4_is_compressed(compressed_data, compressed_size)) {
        *decompressed_data = g_malloc(compressed_size);
        memcpy(*decompressed_data, compressed_data, compressed_size);
        *decompressed_size = compressed_size;
        return true;
    }
    
    qemu_log_mask(LOG_UNIMP, "IMG4 decompression not yet implemented\n");
    return false;
}
