/*
 * Apple T8103 (M1) SoC.
 *
 * Copyright (c) 2025 <PERSON>.
 * Based on T8030 implementation by Visual Ehrmanntraut and Christian Inci.
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, see <http://www.gnu.org/licenses/>.
 */

#include "qemu/osdep.h"
#include "exec/address-spaces.h"
#include "exec/memattrs.h"
#include "exec/memory.h"
#include "hw/arm/apple-silicon/m1.h"
#include "hw/arm/apple-silicon/boot.h"
#include "hw/arm/apple-silicon/dart.h"
#include "hw/arm/apple-silicon/dtb.h"
#include "hw/arm/apple-silicon/lm-backlight.h"
#include "hw/arm/apple-silicon/mem.h"
#include "hw/arm/apple-silicon/mt-spi.h"
#include "hw/arm/apple-silicon/sart.h"
#include "hw/arm/apple-silicon/sep-sim.h"
#include "hw/arm/apple-silicon/sep.h"
#include "hw/arm/apple-silicon/t8103-config.c.inc"
#include "hw/arm/apple-silicon/t8103.h"
#include "hw/arm/apple-silicon/xnu_pf.h"
#include "hw/audio/apple-silicon/aop-audio.h"
#include "hw/audio/apple-silicon/cs35l27.h"
#include "hw/audio/apple-silicon/cs42l77.h"
#include "hw/block/apple_ans.h"
#include "hw/char/apple_uart.h"
#include "hw/display/apple_displaypipe_v4.h"
#include "hw/dma/apple_sio.h"
#include "hw/gpio/apple_gpio.h"
#include "hw/i2c/apple_i2c.h"
#include "hw/intc/apple_aic.h"
#include "hw/irq.h"
#include "hw/misc/apple-silicon/aes.h"
#include "hw/misc/apple-silicon/aop.h"
#include "hw/misc/apple-silicon/bdif.h"
#include "hw/misc/apple-silicon/cfg.h"
#include "hw/misc/apple_nvram.h"
#include "hw/misc/apple_rtkit.h"
#include "hw/misc/apple_smc.h"
#include "hw/misc/apple_spmi.h"
#include "hw/misc/apple_spmi_pmu.h"
#include "hw/misc/unimp.h"
#include "hw/net/apple_ethernet.h"
#include "hw/pci-host/apcie.h"
#include "hw/spmi/core.h"
#include "hw/ssi/apple_spi.h"
#include "hw/usb/apple_otg.h"
#include "hw/usb/apple_typec.h"
#include "hw/usb/hcd-dwc3.h"
#include "hw/watchdog/apple_wdt.h"
#include "qapi/error.h"
#include "qemu/error-report.h"
#include "qemu/guest-random.h"
#include "qemu/log.h"
#include "qemu/units.h"
#include "system/reset.h"
#include "system/runstate.h"
#include "system/system.h"

/* T8103 Memory Map */
#define T8103_SROM_BASE (0x100000000)
#define T8103_SROM_SIZE (512 * KiB)

#define T8103_SRAM_BASE (0x19C000000)
#define T8103_SRAM_SIZE (0x400000)

#define T8103_DRAM_BASE (0x800000000)

#define T8103_SEPROM_BASE (0x240000000)
#define T8103_SEPROM_SIZE (8 * MiB)

#define T8103_GPIO_FORCE_DFU (161)

#define T8103_DISPLAY_SIZE (68 * MiB)

#define T8103_DWC2_IRQ (495)

#define T8103_NUM_UARTS (9)

/* M1-specific sizes */
#define T8103_ANS_TEXT_SIZE (0x124000)
#define T8103_ANS_DATA_SIZE (0x3C00000)
#define T8103_SMC_SRAM_SIZE (0x4000)
#define T8103_SIO_TEXT_SIZE (0x1C000)
#define T8103_SIO_DATA_SIZE (0xF8000)
#define T8103_PANIC_SIZE (0x100000)

#define T8103_AMCC_BASE (0x200000000)
#define T8103_AMCC_SIZE (0x100000)
#define AMCC_PLANE_COUNT (4)
#define AMCC_PLANE_STRIDE (0x40000)
#define AMCC_LOWER(_p) (0x680 + (_p) * AMCC_PLANE_STRIDE)
#define AMCC_UPPER(_p) (0x684 + (_p) * AMCC_PLANE_STRIDE)
#define AMCC_REG(_tms, _x) *(uint32_t *)(&t8103_machine->amcc_reg[_x])

static uint32_t t8103_real_cpu_count(T8103MachineState *t8103_machine)
{
    uint32_t count = 0;
    for (int i = 0; i < M1_MAX_CPU; i++) {
        if (t8103_machine->cpus[i] != NULL) {
            count++;
        }
    }
    return count;
}

static void t8103_start_cpus(T8103MachineState *t8103_machine, uint64_t cpu_mask)
{
    for (int i = 0; i < M1_MAX_CPU; i++) {
        if ((cpu_mask & (1 << i)) && t8103_machine->cpus[i] != NULL) {
            apple_m1_cpu_start(t8103_machine->cpus[i]);
        }
    }
}

static void t8103_create_s3c_uart(T8103MachineState *t8103_machine, uint32_t port,
                                  Chardev *chr)
{
    DeviceState *dev;
    DTBNode *child;
    DTBProp *prop;
    hwaddr *uart_offset;
    hwaddr base;
    uint32_t *ints;

    child = dtb_get_node(t8103_machine->device_tree, "arm-io/uart0");
    g_assert_nonnull(child);

    prop = dtb_find_prop(child, "reg");
    g_assert_nonnull(prop);

    uart_offset = (hwaddr *)prop->data;
    base = t8103_machine->soc_base_pa + uart_offset[0] + uart_offset[1] * port;

    prop = dtb_find_prop(child, "interrupts");
    g_assert_nonnull(prop);

    dev = apple_uart_create(base, 15, 0, chr,
                           qdev_get_gpio_in(DEVICE(t8103_machine->aic),
                                           ((uint32_t *)prop->data)[0] + port));
    object_property_add_child(OBJECT(t8103_machine), "uart", OBJECT(dev));
}

/* PMGR register operations */
static uint64_t pmgr_reg_read(void *opaque, hwaddr addr, unsigned size)
{
    T8103MachineState *t8103_machine = T8103_MACHINE(opaque);
    uint64_t result = 0;

    memcpy(&result, t8103_machine->pmgr_reg + addr, size);
    return result;
}

static void pmgr_reg_write(void *opaque, hwaddr addr, uint64_t data,
                          unsigned size)
{
    T8103MachineState *t8103_machine = T8103_MACHINE(opaque);
    AppleSEPState *sep;
    uint64_t value = data;

    switch (addr) {
    case 0xD4004:
        t8103_start_cpus(t8103_machine, data);
        return;
    case 0x80C00:
        sep = (AppleSEPState *)object_dynamic_cast(
            object_property_get_link(OBJECT(t8103_machine), "sep",
                                     &error_fatal),
            TYPE_APPLE_SEP);

        if (sep != NULL) {
            if (data & (1 << 31)) {
                apple_m1_cpu_reset(APPLE_M1(sep->cpu));
            } else if (data & (1 << 10)) {
                apple_m1_cpu_off(APPLE_M1(sep->cpu));
            } else {
                if (apple_m1_cpu_is_powered_off(APPLE_M1(sep->cpu))) {
                    apple_m1_cpu_start(APPLE_M1(sep->cpu));
                }
            }
        }
        break;
    }
    memcpy(t8103_machine->pmgr_reg + addr, &value, size);
}

static const MemoryRegionOps pmgr_reg_ops = {
    .read = pmgr_reg_read,
    .write = pmgr_reg_write,
    .endianness = DEVICE_NATIVE_ENDIAN,
    .impl.min_access_size = 1,
    .impl.max_access_size = 8,
    .valid.min_access_size = 1,
    .valid.max_access_size = 8,
    .valid.unaligned = false,
};

static uint64_t pmgr_unk_reg_read(void *opaque, hwaddr addr, unsigned size)
{
    return 0;
}

static void pmgr_unk_reg_write(void *opaque, hwaddr addr, uint64_t data,
                              unsigned size)
{
    /* Ignore writes to unknown PMGR registers */
}

static const MemoryRegionOps pmgr_unk_reg_ops = {
    .read = pmgr_unk_reg_read,
    .write = pmgr_unk_reg_write,
    .endianness = DEVICE_NATIVE_ENDIAN,
    .impl.min_access_size = 1,
    .impl.max_access_size = 8,
    .valid.min_access_size = 1,
    .valid.max_access_size = 8,
    .valid.unaligned = false,
};

/* CPU setup and management */
static void t8103_cpu_setup(T8103MachineState *t8103_machine)
{
    DTBNode *child;
    DTBProp *prop;
    uint32_t *cpu_ids;
    uint32_t cpu_count;
    uint32_t cluster_id = 0;
    uint32_t cpu_in_cluster = 0;
    char name[32];

    child = dtb_get_node(t8103_machine->device_tree, "cpus");
    g_assert_nonnull(child);

    prop = dtb_find_prop(child, "cpu-ids");
    g_assert_nonnull(prop);

    cpu_ids = (uint32_t *)prop->data;
    cpu_count = prop->length / sizeof(uint32_t);

    /* Create CPU clusters */
    for (int i = 0; i < M1_MAX_CLUSTER; i++) {
        AppleM1Cluster *cluster = &t8103_machine->clusters[i];

        object_initialize_child(OBJECT(t8103_machine), "cluster", cluster,
                               TYPE_APPLE_M1_CLUSTER);

        /* Set cluster type: 0 = Icestorm (efficiency), 1 = Firestorm (performance) */
        cluster->cluster_type = i;

        qdev_realize(DEVICE(cluster), NULL, &error_fatal);
    }

    /* Create CPUs */
    for (int i = 0; i < cpu_count && i < M1_MAX_CPU; i++) {
        uint8_t cluster_type;

        /* First 4 CPUs are Icestorm (efficiency), next 4 are Firestorm (performance) */
        if (i < 4) {
            cluster_type = 'E'; /* Efficiency */
            cluster_id = 0;
            cpu_in_cluster = i;
        } else {
            cluster_type = 'P'; /* Performance */
            cluster_id = 1;
            cpu_in_cluster = i - 4;
        }

        snprintf(name, sizeof(name), "cpu%d", i);

        t8103_machine->cpus[i] = apple_m1_cpu_create(child, name, i, cpu_ids[i],
                                                     cluster_id, cluster_type);

        if (t8103_machine->cpus[i]) {
            t8103_machine->clusters[cluster_id].cpus[cpu_in_cluster] = t8103_machine->cpus[i];
        }
    }
}

/* AIC (Apple Interrupt Controller) setup */
static void t8103_create_aic(T8103MachineState *t8103_machine)
{
    int i;
    DTBNode *child;
    DTBProp *prop;
    uint64_t *reg;

    child = dtb_get_node(t8103_machine->device_tree, "arm-io/aic");
    g_assert_nonnull(child);

    t8103_machine->aic = SYS_BUS_DEVICE(qdev_new(TYPE_APPLE_AIC));
    object_property_add_child(OBJECT(t8103_machine), "aic",
                             OBJECT(t8103_machine->aic));

    prop = dtb_find_prop(child, "reg");
    g_assert_nonnull(prop);

    reg = (uint64_t *)prop->data;

    for (i = 0; i < t8103_real_cpu_count(t8103_machine); i++) {
        memory_region_add_subregion_overlap(
            &t8103_machine->cpus[i]->memory,
            t8103_machine->soc_base_pa + reg[0],
            sysbus_mmio_get_region(t8103_machine->aic, i), 0);
        sysbus_connect_irq(
            t8103_machine->aic, i,
            qdev_get_gpio_in(DEVICE(t8103_machine->cpus[i]), ARM_CPU_IRQ));
    }

    sysbus_realize_and_unref(t8103_machine->aic, &error_fatal);
}

/* PMGR (Power Management) setup */
static void t8103_create_pmgr(T8103MachineState *t8103_machine)
{
    DTBNode *child;
    DTBProp *prop;
    uint64_t *reg;
    MemoryRegion *mem;
    char name[32];

    child = dtb_get_node(t8103_machine->device_tree, "arm-io/pmgr");
    g_assert_nonnull(child);

    prop = dtb_find_prop(child, "reg");
    g_assert_nonnull(prop);

    reg = (uint64_t *)prop->data;

    for (int i = 0; i < prop->length / 16; i++) {
        mem = g_new(MemoryRegion, 1);
        snprintf(name, 32, "pmgr-reg-%d", i);
        if (i == 0) {
            memory_region_init_io(mem, OBJECT(t8103_machine), &pmgr_reg_ops,
                                  t8103_machine, "pmgr-reg", reg[i + 1]);
        } else {
            memory_region_init_io(mem, OBJECT(t8103_machine), &pmgr_unk_reg_ops,
                                  (void *)reg[i], name, reg[i + 1]);
        }
        memory_region_add_subregion(get_system_memory(),
                                    reg[i] + reg[i + 1] <
                                            t8103_machine->soc_size ?
                                        t8103_machine->soc_base_pa + reg[i] :
                                        reg[i],
                                    mem);
    }

    /* Add PMP register region */
    {
        mem = g_new(MemoryRegion, 1);

        snprintf(name, 32, "pmp-reg");
        memory_region_init_io(mem, OBJECT(t8103_machine), &pmgr_unk_reg_ops,
                              (void *)0x3BC00000, name, 0x60000);
        memory_region_add_subregion(
            get_system_memory(), t8103_machine->soc_base_pa + 0x3BC00000, mem);
    }

    /* Set voltage states in device tree */
    dtb_set_prop(child, "voltage-states5", sizeof(t8103_voltage_states5),
                 t8103_voltage_states5);
    dtb_set_prop(child, "voltage-states9-sram",
                 sizeof(t8103_voltage_states9_sram),
                 t8103_voltage_states9_sram);
    dtb_set_prop(child, "voltage-states0", sizeof(t8103_voltage_states0),
                 t8103_voltage_states0);
    dtb_set_prop(child, "voltage-states9", sizeof(t8103_voltage_states9),
                 t8103_voltage_states9);
    dtb_set_prop(child, "voltage-states2", sizeof(t8103_voltage_states2),
                 t8103_voltage_states2);
    dtb_set_prop(child, "voltage-states1-sram",
                 sizeof(t8103_voltage_states1_sram),
                 t8103_voltage_states1_sram);
    dtb_set_prop(child, "voltage-states10", sizeof(t8103_voltage_states10),
                 t8103_voltage_states10);
    dtb_set_prop(child, "voltage-states11", sizeof(t8103_voltage_states11),
                 t8103_voltage_states11);
    dtb_set_prop(child, "voltage-states8", sizeof(t8103_voltage_states8),
                 t8103_voltage_states8);
    dtb_set_prop(child, "voltage-states5-sram",
                 sizeof(t8103_voltage_states5_sram),
                 t8103_voltage_states5_sram);
    dtb_set_prop(child, "voltage-states1", sizeof(t8103_voltage_states1),
                 t8103_voltage_states1);
}

/* AMCC (Apple Memory Cache Controller) operations */
static uint64_t amcc_reg_read(void *opaque, hwaddr addr, unsigned size)
{
    T8103MachineState *t8103_machine = T8103_MACHINE(opaque);
    uint64_t result = 0;
    uint64_t base =
        t8103_machine->boot_info.top_of_kernel_data_pa - T8103_DRAM_BASE;
    uint64_t amcc_size = 0xf000000;

    switch (addr) {
    case 0x6A0:
    case 0x406A0:
    case 0x806A0:
    case 0xC06A0:
        result = base >> 14;
        break;
    case 0x6A4:
    case 0x406A4:
    case 0x806A4:
    case 0xC06A4:
        result = (base + amcc_size) >> 14;
        break;
    default:
        memcpy(&result, t8103_machine->amcc_reg + addr, size);
        break;
    }
    return result;
}

static void amcc_reg_write(void *opaque, hwaddr addr, uint64_t data,
                          unsigned size)
{
    T8103MachineState *t8103_machine = T8103_MACHINE(opaque);
    uint64_t value = data;
    memcpy(t8103_machine->amcc_reg + addr, &value, size);
}

static const MemoryRegionOps amcc_reg_ops = {
    .read = amcc_reg_read,
    .write = amcc_reg_write,
    .endianness = DEVICE_NATIVE_ENDIAN,
    .impl.min_access_size = 1,
    .impl.max_access_size = 8,
    .valid.min_access_size = 1,
    .valid.max_access_size = 8,
    .valid.unaligned = false,
};

/* AMCC setup */
static void t8103_create_amcc(T8103MachineState *t8103_machine)
{
    DTBNode *child;

    child = dtb_get_node(t8103_machine->device_tree, "arm-io/amcc");
    g_assert_nonnull(child);

    dtb_set_prop_u32(child, "aperture-count", 1);
    dtb_set_prop_u32(child, "aperture-size", 0x100000);
    dtb_set_prop_u32(child, "plane-count", AMCC_PLANE_COUNT);
    dtb_set_prop_u32(child, "plane-stride", AMCC_PLANE_STRIDE);
    dtb_set_prop_hwaddr(child, "aperture-phys-addr", T8103_AMCC_BASE);
    dtb_set_prop_u32(child, "cache-status-reg-offset", 0x1C00);
    dtb_set_prop_u32(child, "cache-status-reg-mask", 0x1F);
    dtb_set_prop_u32(child, "cache-status-reg-value", 0);

    child = dtb_get_node(child, "amcc-ctrr-a");
    g_assert_nonnull(child);

    dtb_set_prop_u32(child, "page-size-shift", 14);
    dtb_set_prop_u32(child, "lower-limit-reg-offset", AMCC_LOWER(0));
    dtb_set_prop_u32(child, "lower-limit-reg-mask", 0xFFFFFFFF);
    dtb_set_prop_u32(child, "upper-limit-reg-offset", AMCC_UPPER(0));
    dtb_set_prop_u32(child, "upper-limit-reg-mask", 0xFFFFFFFF);
    dtb_set_prop_u32(child, "lock-reg-offset", 0x68C);
    dtb_set_prop_u32(child, "lock-reg-mask", 1);
    dtb_set_prop_u32(child, "lock-reg-value", 1);

    memory_region_init_io(&t8103_machine->amcc, OBJECT(t8103_machine),
                          &amcc_reg_ops, t8103_machine, "amcc",
                          T8103_AMCC_SIZE);
    memory_region_add_subregion(get_system_memory(), T8103_AMCC_BASE,
                                &t8103_machine->amcc);
}

/* Machine initialization */
static void t8103_machine_init_done(Notifier *notifier, void *data)
{
    T8103MachineState *t8103_machine =
        container_of(notifier, T8103MachineState, init_done_notifier);

    /* Perform any post-initialization setup here */
    qemu_log_mask(LOG_GUEST_ERROR, "T8103 machine initialization complete\n");
}

static void t8103_machine_init(MachineState *machine)
{
    T8103MachineState *t8103_machine;
    MachoHeader64 *hdr;
    uint64_t kernel_low, kernel_high;
    uint32_t build_version;

    t8103_machine = T8103_MACHINE(machine);

    if (!machine->kernel_filename) {
        error_setg(&error_fatal, "No kernel image specified");
        return;
    }

    /* Load kernel */
    hdr = macho_load_file(machine->kernel_filename);
    if (!hdr) {
        error_setg(&error_fatal, "Could not load kernel from %s",
                   machine->kernel_filename);
        return;
    }

    t8103_machine->kernel = hdr;
    macho_get_buffer_extent(hdr, &kernel_low, &kernel_high);

    /* Extract build version from kernel */
    build_version = macho_build_version(hdr);
    t8103_machine->build_version = build_version;

    /* Load device tree */
    if (!machine->dtb) {
        error_setg(&error_fatal, "No device tree specified");
        return;
    }

    t8103_machine->device_tree = load_dtb_from_file(machine->dtb);
    if (!t8103_machine->device_tree) {
        error_setg(&error_fatal, "Could not load device tree from %s",
                   machine->dtb);
        return;
    }

    /* Set up memory regions */
    allocate_ram(get_system_memory(), "SROM", T8103_SROM_BASE, T8103_SROM_SIZE, 0);
    allocate_ram(get_system_memory(), "SRAM", T8103_SRAM_BASE, T8103_SRAM_SIZE, 0);
    memory_region_add_subregion(get_system_memory(), T8103_DRAM_BASE, machine->ram);

    if (t8103_machine->sep_rom_filename) {
        allocate_ram(get_system_memory(), "SEPROM", T8103_SEPROM_BASE,
                     T8103_SEPROM_SIZE, 0);
    }

    /* Initialize SoC base address from device tree */
    DTBNode *arm_io = dtb_get_node(t8103_machine->device_tree, "arm-io");
    g_assert_nonnull(arm_io);

    DTBProp *prop = dtb_find_prop(arm_io, "ranges");
    g_assert_nonnull(prop);

    hwaddr *ranges = (hwaddr *)prop->data;
    t8103_machine->soc_base_pa = ranges[1];
    t8103_machine->soc_size = ranges[2];

    /* Set platform properties */
    dtb_set_prop_strn(t8103_machine->device_tree, "platform-name", 32, "t8103");
    dtb_set_prop_strn(t8103_machine->device_tree, "model-number", 32,
                      t8103_machine->model_number ?: "MacBookAir10,1");

    /* Initialize components */
    t8103_cpu_setup(t8103_machine);
    t8103_create_aic(t8103_machine);
    t8103_create_pmgr(t8103_machine);
    t8103_create_amcc(t8103_machine);

    /* Create UARTs */
    for (int i = 0; i < T8103_NUM_UARTS; i++) {
        t8103_create_s3c_uart(t8103_machine, i, serial_hd(i));
    }

    t8103_machine->init_done_notifier.notify = t8103_machine_init_done;
    qemu_add_machine_init_done_notifier(&t8103_machine->init_done_notifier);
}

static void t8103_machine_class_init(ObjectClass *oc, void *data)
{
    MachineClass *mc = MACHINE_CLASS(oc);

    mc->desc = "Apple T8103 (M1) SoC";
    mc->init = t8103_machine_init;
    mc->max_cpus = M1_MAX_CPU;
    mc->default_cpus = M1_MAX_CPU;
    mc->default_ram_size = 8 * GiB;
    mc->default_ram_id = "ram";
    mc->minimum_page_bits = 14; /* 16KB pages */
    mc->block_default_type = IF_NONE;
    mc->no_cdrom = 1;
    mc->no_floppy = 1;
    mc->no_parallel = 1;
    mc->default_machine_opts = "graphics=off";
}

/* MacBook Air 10,1 machine */
static void macbookair10_1_machine_init(MachineState *machine)
{
    T8103MachineState *t8103_machine = T8103_MACHINE(machine);

    /* Set MacBook Air 10,1 specific properties */
    t8103_machine->board_id = 0x22; /* MacBook Air 10,1 board ID */
    t8103_machine->chip_revision = 0x1; /* M1 revision 1 */
    t8103_machine->model_number = g_strdup("MacBookAir10,1");
    t8103_machine->region_info = g_strdup("LL/A");
    t8103_machine->config_number = g_strdup("MGN63");
    t8103_machine->serial_number = g_strdup("C02D12345678");
    t8103_machine->mlb_serial_number = g_strdup("C02012345ABCDEFGH");
    t8103_machine->regulatory_model = g_strdup("A2337");

    /* Call base T8103 initialization */
    t8103_machine_init(machine);
}

static void macbookair10_1_machine_class_init(ObjectClass *oc, void *data)
{
    MachineClass *mc = MACHINE_CLASS(oc);

    /* Call parent class init */
    t8103_machine_class_init(oc, data);

    mc->desc = "Apple MacBook Air (M1, 2020) - MacBookAir10,1";
    mc->init = macbookair10_1_machine_init;
    mc->alias = "macbookair10,1";
}

static const TypeInfo t8103_machine_info = {
    .name = TYPE_T8103_MACHINE,
    .parent = TYPE_MACHINE,
    .instance_size = sizeof(T8103MachineState),
    .class_size = sizeof(T8103MachineClass),
    .class_init = t8103_machine_class_init,
    .abstract = true,
};

static const TypeInfo macbookair10_1_machine_info = {
    .name = MACHINE_TYPE_NAME("macbookair10,1"),
    .parent = TYPE_T8103_MACHINE,
    .class_init = macbookair10_1_machine_class_init,
};

static void t8103_machine_types(void)
{
    type_register_static(&t8103_machine_info);
    type_register_static(&macbookair10_1_machine_info);
}

type_init(t8103_machine_types)
