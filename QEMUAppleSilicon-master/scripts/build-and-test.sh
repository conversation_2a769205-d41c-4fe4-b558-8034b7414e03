#!/bin/bash
#
# Apple T8103 (M1) QEMU Build and Test Script
#
# Copyright (c) 2025 <PERSON>.
#
# This script builds the complete T8103 QEMU implementation and runs
# comprehensive tests to validate the implementation.
#

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
QEMU_DIR="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$QEMU_DIR/build"
FIRMWARE_DIR="../firmware_extracted/extracted_components"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Build QEMU with Apple Silicon support
build_qemu() {
    log_info "Building QEMU with Apple Silicon support..."
    
    cd "$QEMU_DIR"
    
    # Clean previous build if requested
    if [ "$1" = "clean" ]; then
        log_info "Cleaning previous build..."
        rm -rf "$BUILD_DIR"
    fi
    
    # Configure QEMU
    if [ ! -f "$BUILD_DIR/config.status" ]; then
        log_info "Configuring QEMU..."
        ./configure \
            --target-list=aarch64-softmmu \
            --enable-apple-silicon \
            --enable-debug \
            --enable-debug-info \
            --enable-guest-agent \
            --enable-vnc \
            --enable-gtk \
            --enable-sdl \
            --disable-werror \
            --prefix="$BUILD_DIR/install"
    fi
    
    # Build QEMU
    log_info "Building QEMU (this may take several minutes)..."
    make -j$(nproc)
    
    log_success "QEMU build completed successfully"
}

# Validate build artifacts
validate_build() {
    log_info "Validating build artifacts..."
    
    local qemu_binary="$BUILD_DIR/qemu-system-aarch64"
    
    if [ ! -f "$qemu_binary" ]; then
        log_error "QEMU binary not found: $qemu_binary"
        return 1
    fi
    
    # Test basic functionality
    if ! "$qemu_binary" -version > /dev/null 2>&1; then
        log_error "QEMU binary is not functional"
        return 1
    fi
    
    # Check if our machine type is available
    if ! "$qemu_binary" -M help | grep -q "macbookair10,1"; then
        log_error "MacBook Air 10,1 machine type not found"
        return 1
    fi
    
    # Check if our CPU models are available
    if ! "$qemu_binary" -cpu help | grep -q "apple-firestorm"; then
        log_error "Apple Firestorm CPU model not found"
        return 1
    fi
    
    if ! "$qemu_binary" -cpu help | grep -q "apple-icestorm"; then
        log_error "Apple Icestorm CPU model not found"
        return 1
    fi
    
    log_success "Build validation completed successfully"
}

# Run comprehensive tests
run_tests() {
    log_info "Running comprehensive tests..."
    
    cd "$QEMU_DIR"
    
    # Run our custom test suite
    if [ -f "scripts/test-t8103.sh" ]; then
        log_info "Running T8103-specific tests..."
        bash scripts/test-t8103.sh
    else
        log_warning "T8103 test script not found, skipping specific tests"
    fi
    
    log_success "Test execution completed"
}

# Create example boot command
create_boot_example() {
    log_info "Creating example boot commands..."
    
    local example_file="$QEMU_DIR/BOOT_EXAMPLES.md"
    
    cat > "$example_file" << 'EOF'
# Apple T8103 (M1) QEMU Boot Examples

## Basic Boot with Extracted Firmware

```bash
# Boot MacBook Air M1 with extracted kernelcache and device tree
./build/qemu-system-aarch64 \
  -M macbookair10,1 \
  -cpu apple-firestorm \
  -smp 8 \
  -m 8G \
  -kernel ../firmware_extracted/extracted_components/j313_kernelcache.bin \
  -dtb ../firmware_extracted/extracted_components/j313_devicetree.bin \
  -serial stdio \
  -nographic

# Boot with debug logging enabled
./build/qemu-system-aarch64 \
  -M macbookair10,1 \
  -cpu apple-firestorm \
  -smp 8 \
  -m 8G \
  -kernel ../firmware_extracted/extracted_components/j313_kernelcache.bin \
  -dtb ../firmware_extracted/extracted_components/j313_devicetree.bin \
  -serial stdio \
  -nographic \
  -d guest_errors,unimp \
  -D qemu_debug.log

# Boot with VNC display (for framebuffer testing)
./build/qemu-system-aarch64 \
  -M macbookair10,1 \
  -cpu apple-firestorm \
  -smp 8 \
  -m 8G \
  -kernel ../firmware_extracted/extracted_components/j313_kernelcache.bin \
  -dtb ../firmware_extracted/extracted_components/j313_devicetree.bin \
  -serial stdio \
  -vnc :1

# Boot with monitor console for debugging
./build/qemu-system-aarch64 \
  -M macbookair10,1 \
  -cpu apple-firestorm \
  -smp 8 \
  -m 8G \
  -kernel ../firmware_extracted/extracted_components/j313_kernelcache.bin \
  -dtb ../firmware_extracted/extracted_components/j313_devicetree.bin \
  -serial stdio \
  -monitor telnet:localhost:4444,server,nowait
```

## Testing Different CPU Configurations

```bash
# Test with Icestorm (efficiency) cores
./build/qemu-system-aarch64 \
  -M macbookair10,1 \
  -cpu apple-icestorm \
  -smp 4 \
  -m 8G \
  -kernel ../firmware_extracted/extracted_components/j313_kernelcache.bin \
  -dtb ../firmware_extracted/extracted_components/j313_devicetree.bin \
  -serial stdio \
  -nographic

# Test with mixed core configuration (if supported)
./build/qemu-system-aarch64 \
  -M macbookair10,1 \
  -cpu apple-firestorm \
  -smp 8 \
  -m 16G \
  -kernel ../firmware_extracted/extracted_components/j313_kernelcache.bin \
  -dtb ../firmware_extracted/extracted_components/j313_devicetree.bin \
  -serial stdio \
  -nographic
```

## Debug and Development

```bash
# Boot with GDB debugging
./build/qemu-system-aarch64 \
  -M macbookair10,1 \
  -cpu apple-firestorm \
  -smp 8 \
  -m 8G \
  -kernel ../firmware_extracted/extracted_components/j313_kernelcache.bin \
  -dtb ../firmware_extracted/extracted_components/j313_devicetree.bin \
  -serial stdio \
  -nographic \
  -s -S

# Then in another terminal:
# gdb-multiarch
# (gdb) target remote localhost:1234
# (gdb) continue

# Boot with detailed device tracing
./build/qemu-system-aarch64 \
  -M macbookair10,1 \
  -cpu apple-firestorm \
  -smp 8 \
  -m 8G \
  -kernel ../firmware_extracted/extracted_components/j313_kernelcache.bin \
  -dtb ../firmware_extracted/extracted_components/j313_devicetree.bin \
  -serial stdio \
  -nographic \
  -trace "apple_*" \
  -D trace.log
```

## Expected Boot Behavior

1. **Early Boot**: QEMU should initialize the T8103 machine and load the kernelcache
2. **Device Initialization**: AIC, DART, ANE, and GPU stubs should be created
3. **CPU Startup**: Firestorm/Icestorm cores should be initialized
4. **Kernel Loading**: The macOS kernelcache should begin execution
5. **Debug Output**: Serial console should show boot progress and debug messages

## Troubleshooting

- If boot hangs early, check that the kernelcache and device tree are valid
- If devices are not recognized, verify the device tree contains proper Apple Silicon entries
- For debugging, use the monitor console: `info registers`, `info qtree`, `info mtree`
- Check debug logs in `t8103_debug.log` for detailed boot information
EOF

    log_success "Boot examples created in $example_file"
}

# Main execution
main() {
    local command="${1:-build}"
    
    log_info "Apple T8103 (M1) QEMU Build and Test System"
    log_info "============================================="
    
    case "$command" in
        "build")
            build_qemu
            validate_build
            create_boot_example
            ;;
        "clean")
            build_qemu clean
            validate_build
            create_boot_example
            ;;
        "test")
            validate_build
            run_tests
            ;;
        "all")
            build_qemu
            validate_build
            run_tests
            create_boot_example
            ;;
        *)
            log_info "Usage: $0 [build|clean|test|all]"
            log_info "  build - Build QEMU with Apple Silicon support"
            log_info "  clean - Clean build and rebuild from scratch"
            log_info "  test  - Run validation tests"
            log_info "  all   - Build, validate, and test"
            exit 1
            ;;
    esac
    
    log_success "Operation completed successfully!"
}

# Run main function
main "$@"
