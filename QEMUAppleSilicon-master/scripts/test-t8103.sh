#!/bin/bash
#
# Apple T8103 (M1) QEMU Integration Test Script
#
# Copyright (c) 2025 <PERSON>.
#
# This script tests the complete T8103 QEMU implementation with real
# macOS kernelcache and validates the boot process through various stages.
#

set -e

# Configuration
QEMU_BINARY="./build/qemu-system-aarch64"
MACHINE_TYPE="macbookair10,1"
FIRMWARE_DIR="../firmware_extracted/extracted_components"
LOG_DIR="./test_logs"
TEST_TIMEOUT=300  # 5 minutes

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test result tracking
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

test_result() {
    local test_name="$1"
    local result="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    if [ "$result" = "PASS" ]; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
        log_success "Test '$test_name': PASSED"
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
        log_error "Test '$test_name': FAILED - $result"
    fi
}

# Setup test environment
setup_test_env() {
    log_info "Setting up test environment..."
    
    # Create log directory
    mkdir -p "$LOG_DIR"
    
    # Check if QEMU binary exists
    if [ ! -f "$QEMU_BINARY" ]; then
        log_error "QEMU binary not found: $QEMU_BINARY"
        log_info "Please build QEMU first with: make -j\$(nproc)"
        exit 1
    fi
    
    # Check if firmware files exist
    if [ ! -d "$FIRMWARE_DIR" ]; then
        log_error "Firmware directory not found: $FIRMWARE_DIR"
        log_info "Please extract firmware files first"
        exit 1
    fi
    
    log_success "Test environment setup complete"
}

# Test 1: QEMU binary and machine type validation
test_qemu_binary() {
    log_info "Testing QEMU binary and machine type..."
    
    # Test if QEMU can list machines
    if $QEMU_BINARY -M help | grep -q "$MACHINE_TYPE"; then
        test_result "QEMU Binary & Machine Type" "PASS"
    else
        test_result "QEMU Binary & Machine Type" "Machine type $MACHINE_TYPE not found"
    fi
}

# Test 2: CPU model validation
test_cpu_models() {
    log_info "Testing CPU models..."
    
    # Test Firestorm CPU
    if $QEMU_BINARY -cpu help | grep -q "apple-firestorm"; then
        test_result "Firestorm CPU Model" "PASS"
    else
        test_result "Firestorm CPU Model" "apple-firestorm CPU not found"
    fi
    
    # Test Icestorm CPU
    if $QEMU_BINARY -cpu help | grep -q "apple-icestorm"; then
        test_result "Icestorm CPU Model" "PASS"
    else
        test_result "Icestorm CPU Model" "apple-icestorm CPU not found"
    fi
}

# Test 3: Basic machine instantiation
test_machine_instantiation() {
    log_info "Testing basic machine instantiation..."
    
    local log_file="$LOG_DIR/machine_instantiation.log"
    
    # Try to instantiate the machine without kernel (should fail gracefully)
    timeout 10 $QEMU_BINARY \
        -M "$MACHINE_TYPE" \
        -smp 8 \
        -m 8G \
        -nographic \
        -serial file:"$log_file" \
        2>&1 | head -20 > "$log_file.stderr" || true
    
    # Check if machine was created (should show error about missing kernel)
    if grep -q "No kernel image specified" "$log_file.stderr" 2>/dev/null; then
        test_result "Machine Instantiation" "PASS"
    else
        test_result "Machine Instantiation" "Machine failed to instantiate properly"
    fi
}

# Test 4: Firmware file validation
test_firmware_files() {
    log_info "Testing firmware files..."
    
    local kernelcache="$FIRMWARE_DIR/j313_kernelcache.bin"
    local devicetree="$FIRMWARE_DIR/j313_devicetree.bin"
    local sep_firmware="$FIRMWARE_DIR/j313_sep_firmware.bin"
    
    # Check kernelcache
    if [ -f "$kernelcache" ] && [ -s "$kernelcache" ]; then
        test_result "Kernelcache File" "PASS"
    else
        test_result "Kernelcache File" "Missing or empty: $kernelcache"
    fi
    
    # Check device tree
    if [ -f "$devicetree" ] && [ -s "$devicetree" ]; then
        test_result "Device Tree File" "PASS"
    else
        test_result "Device Tree File" "Missing or empty: $devicetree"
    fi
    
    # Check SEP firmware
    if [ -f "$sep_firmware" ] && [ -s "$sep_firmware" ]; then
        test_result "SEP Firmware File" "PASS"
    else
        test_result "SEP Firmware File" "Missing or empty: $sep_firmware"
    fi
}

# Test 5: Boot attempt with real firmware
test_boot_attempt() {
    log_info "Testing boot attempt with real firmware..."
    
    local kernelcache="$FIRMWARE_DIR/j313_kernelcache.bin"
    local devicetree="$FIRMWARE_DIR/j313_devicetree.bin"
    local log_file="$LOG_DIR/boot_attempt.log"
    local debug_log="$LOG_DIR/t8103_debug.log"
    
    if [ ! -f "$kernelcache" ] || [ ! -f "$devicetree" ]; then
        test_result "Boot Attempt" "Required firmware files missing"
        return
    fi
    
    log_info "Attempting boot (timeout: ${TEST_TIMEOUT}s)..."
    
    # Attempt to boot with real firmware
    timeout $TEST_TIMEOUT $QEMU_BINARY \
        -M "$MACHINE_TYPE" \
        -cpu apple-firestorm \
        -smp 8 \
        -m 8G \
        -kernel "$kernelcache" \
        -dtb "$devicetree" \
        -nographic \
        -serial file:"$log_file" \
        -d guest_errors,unimp \
        2>&1 > "$log_file.stderr" || true
    
    # Analyze boot logs
    local boot_result="UNKNOWN"
    
    if [ -f "$debug_log" ]; then
        if grep -q "MACHINE_READY" "$debug_log"; then
            boot_result="Machine initialization completed"
        fi
        
        if grep -q "KERNEL" "$debug_log"; then
            boot_result="Kernel loading detected"
        fi
        
        if grep -q "PANIC" "$debug_log"; then
            boot_result="Kernel panic detected"
        fi
    fi
    
    if [ -f "$log_file" ]; then
        if grep -q "Darwin" "$log_file"; then
            boot_result="Darwin kernel detected"
        fi
        
        if grep -q "panic" "$log_file"; then
            boot_result="Kernel panic in serial output"
        fi
    fi
    
    if [ "$boot_result" = "UNKNOWN" ]; then
        test_result "Boot Attempt" "No recognizable boot output"
    else
        test_result "Boot Attempt" "PASS - $boot_result"
    fi
}

# Test 6: Device enumeration
test_device_enumeration() {
    log_info "Testing device enumeration..."
    
    local log_file="$LOG_DIR/device_enum.log"
    
    # Run QEMU with device enumeration
    timeout 30 $QEMU_BINARY \
        -M "$MACHINE_TYPE" \
        -smp 8 \
        -m 8G \
        -nographic \
        -monitor stdio \
        2>&1 > "$log_file" << 'EOF' || true
info qtree
quit
EOF
    
    # Check if key devices are present
    local devices_found=0
    
    if grep -q "apple-aic" "$log_file" 2>/dev/null; then
        devices_found=$((devices_found + 1))
    fi
    
    if grep -q "apple-ane" "$log_file" 2>/dev/null; then
        devices_found=$((devices_found + 1))
    fi
    
    if grep -q "apple-gpu" "$log_file" 2>/dev/null; then
        devices_found=$((devices_found + 1))
    fi
    
    if [ $devices_found -ge 2 ]; then
        test_result "Device Enumeration" "PASS - Found $devices_found key devices"
    else
        test_result "Device Enumeration" "Only found $devices_found key devices"
    fi
}

# Test 7: Memory layout validation
test_memory_layout() {
    log_info "Testing memory layout..."
    
    local log_file="$LOG_DIR/memory_layout.log"
    
    # Run QEMU with memory info
    timeout 30 $QEMU_BINARY \
        -M "$MACHINE_TYPE" \
        -smp 8 \
        -m 8G \
        -nographic \
        -monitor stdio \
        2>&1 > "$log_file" << 'EOF' || true
info mtree
quit
EOF
    
    # Check for key memory regions
    local regions_found=0
    
    if grep -q "800000000" "$log_file" 2>/dev/null; then  # DRAM base
        regions_found=$((regions_found + 1))
    fi
    
    if grep -q "100000000" "$log_file" 2>/dev/null; then  # SOC base
        regions_found=$((regions_found + 1))
    fi
    
    if [ $regions_found -ge 1 ]; then
        test_result "Memory Layout" "PASS - Found $regions_found key memory regions"
    else
        test_result "Memory Layout" "Key memory regions not found"
    fi
}

# Main test execution
main() {
    log_info "Starting T8103 QEMU Integration Tests"
    log_info "====================================="
    
    setup_test_env
    
    # Run all tests
    test_qemu_binary
    test_cpu_models
    test_machine_instantiation
    test_firmware_files
    test_boot_attempt
    test_device_enumeration
    test_memory_layout
    
    # Print summary
    echo
    log_info "Test Summary"
    log_info "============"
    log_info "Total tests: $TESTS_TOTAL"
    log_success "Passed: $TESTS_PASSED"
    log_error "Failed: $TESTS_FAILED"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        log_success "All tests passed!"
        exit 0
    else
        log_error "Some tests failed. Check logs in $LOG_DIR"
        exit 1
    fi
}

# Run main function
main "$@"
