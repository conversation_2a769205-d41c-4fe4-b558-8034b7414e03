<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>PreBootPaths</key>     <!-- stored at root of any Apple_Boot -->
    <dict>
        <key>DiskLabel</key>    <!-- to be tweaked for the picker -->
        <string>/System/Library/CoreServices/.disk_label</string>
        <!-- implied .disk_label helper files -->
        <key>AdditionalPaths</key>  <!-- optional stuff at the root -->
        <array>
            <string>/.VolumeIcon.icns</string>
            <string>/System/Library/CoreServices/SystemVersion.plist</string>
            <string>/System/Library/CoreServices/PlatformSupport.plist</string>
        </array>
    </dict>

    <key>BooterPaths</key>      <!-- to be blessed appropriately -->
    <dict>
        <key>EFIBooter</key>    <!-- finderinfo[1] -> file -->
        <string>/System/Library/CoreServices/boot.efi</string>
    </dict>

    <key>PostBootPaths</key>    <!-- in RPS directories known to booter -->
    <dict>
        <key>BootConfig</key>   <!-- to be updated w/UUID in Apple_Boot -->
        <string>/Library/Preferences/SystemConfiguration/com.apple.Boot.plist</string>
        <key>EncryptedRoot</key>
        <dict>
            <key>EncryptedPropertyCache</key>
            <string>/System/Library/Caches/com.apple.corestorage/EncryptedRoot.plist.wipekey</string>
            <!-- OS doesn't require content in the root volume's ER.pl.wk -->
            <key>RootVolumePropertyCache</key>
            <false/>
            <key>DefaultResourcesDir</key>
            <string>/usr/standalone/i386/EfiLoginUI/</string>
            <!-- localized resources are optional but are all or nothing -->
            <key>LocalizationSource</key>
            <string>/System/Library/PrivateFrameworks/EFILogin.framework/Resources/EFIResourceBuilder.bundle/Contents/Resources</string>
            <key>LanguagesPref</key>
            <string>/Library/Preferences/.GlobalPreferences.plist</string>
            <key>BackgroundImage</key>
            <string>/Library/Caches/com.apple.desktop.admin.png</string>
            <key>LocalizedResourcesCache</key>
            <string>/System/Library/Caches/com.apple.corestorage/EFILoginLocalizations</string>
        </dict>
        <key>Kernelcache v1.6</key>
        <dict>
            <key>ExtensionsDir</key>
            <array>
                <string>/Library/Extensions</string>
                <string>/AppleInternal/Library/Extensions</string>
                <string>/Library/Apple/System/Library/Extensions</string>
                <string>/System/Library/Extensions</string>
            </array>
            <key>Path</key>
            <string>/Library/Apple/System/Library/PrelinkedKernels/prelinkedkernel</string>
            <key>ReadOnlyPath</key>
            <string>/System/Library/PrelinkedKernels/prelinkedkernel</string>
            <key>KernelPath</key>
            <string>/System/Library/Kernels/kernel</string>
            <key>KernelsDir</key>
            <string>/System/Library/Kernels</string>
            <key>BootKernelExtensions</key>
            <string>/System/Library/KernelCollections/BootKernelExtensions.kc</string>
            <key>SystemKernelExtensions</key>
            <string>/System/Library/KernelCollections/SystemKernelExtensions.kc</string>
            <key>BaseSystemKernelExtensions</key>
            <string>/System/Library/KernelCollections/BaseSystemKernelExtensions.kc</string>
            <key>PreferBootKernelExtensions</key>
            <true/>
            <key>Archs</key>
            <array>
                <string>x86_64</string>
            </array>
            <key>Preferred Compression</key>
            <string>lzvn</string>
        </dict>
    </dict>

    <key>bless2</key>
    <dict>
        <key>Version</key>
        <integer>1</integer>
        <key>SupportsPairedRecovery</key>
        <true/>
        <key>SupportsExternalPrebootObjects</key>
        <true/>
        <key>BootObjects</key>
        <dict>
            <key>KernelCache</key>
            <dict>
                <key>DestinationPath</key>
                <string>./System/Library/Caches/com.apple.kernelcaches/kernelcache</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>DeviceTree</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/devicetree.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>ANE</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/ANE.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,ANE1</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,ANE1.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,ANE2</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,ANE2.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,ANE3</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,ANE3.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>AVE</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/AVE.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,AppleVideoEncoder0</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,AppleVideoEncoder0.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,AppleVideoEncoder1</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,AppleVideoEncoder1.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,AppleVideoEncoder2</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,AppleVideoEncoder2.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,AppleVideoEncoder3</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,AppleVideoEncoder3.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,AppleVideoEncoder4</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,AppleVideoEncoder4.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,AppleVideoEncoder5</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,AppleVideoEncoder5.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,AppleVideoEncoder6</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,AppleVideoEncoder6.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,AppleVideoEncoder7</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,AppleVideoEncoder7.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,AppleVideoEncoder8</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,AppleVideoEncoder8.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,AVD</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,AVD.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>AOP</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/AOP.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>AOP2</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/AOP2.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>ISP</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/ISP.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>SIO</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/SIO.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>GFX</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/GFX.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,GFX1Firmware</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,GFX1Firmware.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>PMP</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/PMP.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,DCP2</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/DCP.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>InputDevice</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/InputDevice.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Multitouch</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Multitouch.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>SEP</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/sep-firmware.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>StaticTrustCache</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/StaticTrustCache.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,BaseSystemTrustCache</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/BaseSystemTrustCache.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Diags</key>
            <dict>
                <key>DestinationPath</key>
                <string>./AppleInternal/Diags/bin/diag.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>iBootData</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/iBootData.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>iBoot</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/iBoot.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>PERTOS</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/PERTOS.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>PERTOS1</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/PERTOS1.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>PERTOS2</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/PERTOS2.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>PERTOS3</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/PERTOS3.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>PERTOS4</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/PERTOS4.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>PERTOS5</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/PERTOS5.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>PERTOS6</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/PERTOS6.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>PERTOS7</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/PERTOS7.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>PHLEET</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/PHLEET.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>RBM</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/RBM.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>RBM1</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/RBM1.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>RBM2</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/RBM2.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>RBM3</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/RBM3.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>RBM4</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/RBM4.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>RBM5</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/RBM5.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>RBM6</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/RBM6.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>RBM7</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/RBM7.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>SystemVolume</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/root_hash.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>BaseSystemVolume</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/base_system_root_hash.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>MtpFirmware</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/MtpFirmware.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,SecurePageTableMonitor</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,SecurePageTableMonitor.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,TrustedExecutionMonitor</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,TrustedExecutionMonitor.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,cL4</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,cL4.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,ExclaveOS</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,ExclaveOS.dmg</string>
                <key>ManifestRule</key>
                <integer>0</integer> <!-- unstitched -->
            </dict>
            <key>Ap,ExclaveOSIntegrityCatalog</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,ExclaveOSIntegrityCatalog.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,ExclaveOSTrustCache</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,ExclaveOSTrustCache.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,ExclaveOSVolume</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,ExclaveOSVolume.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,SCodec</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,SCodec.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,MSRFirmware</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,MSRFirmware.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,SecureM3Firmware</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,SecureM3Firmware.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
            <key>Ap,XHC</key>
            <dict>
                <key>DestinationPath</key>
                <string>./usr/standalone/firmware/FUD/Ap,XHC.img4</string>
                <key>ManifestRule</key>
                <integer>1</integer>
            </dict>
        </dict>
    </dict>

</dict>
</plist>
