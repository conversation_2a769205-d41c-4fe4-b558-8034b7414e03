<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildIdentities</key>
	<array>
		<dict>
			<key>Ap,PairedVersionNumber</key>
			<string>25.1.295.5.5,0</string>
			<key>Ap,ProductMarketingVersion</key>
			<string>26.0</string>
			<key>Ap,SDKPlatform</key>
			<string>macosx</string>
			<key>Ap,Target</key>
			<string>BootabilityAP</string>
			<key>Ap,TargetType</key>
			<string>bootability</string>
			<key>ApBoardID</key>
			<string>0xF0</string>
			<key>ApChipID</key>
			<string>0xFF06</string>
			<key>ApSecurityDomain</key>
			<string>0x01</string>
			<key>Info</key>
			<dict>
				<key>Ap,Timestamp</key>
				<integer>0</integer>
				<key>BuildNumber</key>
				<string>25A5295e</string>
				<key>BuildTrain</key>
				<string>CheerSeed</string>
				<key>DeviceClass</key>
				<string>bootabilityap</string>
				<key>FDRSupport</key>
				<false/>
				<key>ImageName</key>
				<string>BootabilityBundle</string>
				<key>MobileDeviceMinVersion</key>
				<string>1810</string>
				<key>RequestManifestProperties</key>
				<array>
					<string>Ap,Timestamp</string>
				</array>
				<key>SystemPartitionPadding</key>
				<dict>
					<key>1024</key>
					<integer>1280</integer>
					<key>128</key>
					<integer>1280</integer>
					<key>16</key>
					<integer>160</integer>
					<key>256</key>
					<integer>1280</integer>
					<key>32</key>
					<integer>320</integer>
					<key>512</key>
					<integer>1280</integer>
					<key>64</key>
					<integer>640</integer>
					<key>768</key>
					<integer>1280</integer>
					<key>8</key>
					<integer>80</integer>
				</dict>
				<key>Variant</key>
				<string>Bootability</string>
				<key>VariantContents</key>
				<dict>
					<key>Bootability</key>
					<string>Bootability</string>
					<key>DCP</key>
					<string>Production</string>
				</dict>
			</dict>
			<key>Manifest</key>
			<dict>
				<key>Ap,BootabilityBrainTrustCache</key>
				<dict>
					<key>Digest</key>
					<data>
					zu5NLjhU1W7lCc9hou9Jka3UbtafzUxmewLK
					tJWkHkgRbntvNETZZvpwl6xEVGo9
					</data>
					<key>Info</key>
					<dict>
						<key>Img4PayloadType</key>
						<string>trbb</string>
						<key>IsFTAB</key>
						<false/>
						<key>IsLoadedByiBoot</key>
						<false/>
						<key>IsLoadedByiBootStage1</key>
						<false/>
						<key>IsiBootEANFirmware</key>
						<false/>
						<key>IsiBootNonEssentialFirmware</key>
						<false/>
						<key>Path</key>
						<string>Firmware/Bootability.dmg.trustcache</string>
						<key>Personalize</key>
						<true/>
						<key>RestoreRequestRules</key>
						<array>
							<dict>
								<key>Actions</key>
								<dict>
									<key>EPRO</key>
									<false/>
								</dict>
								<key>Conditions</key>
								<dict>
									<key>ApCurrentProductionMode</key>
									<false/>
									<key>ApRequiresImage4</key>
									<true/>
								</dict>
							</dict>
							<dict>
								<key>Actions</key>
								<dict>
									<key>EPRO</key>
									<true/>
								</dict>
								<key>Conditions</key>
								<dict>
									<key>ApCurrentProductionMode</key>
									<true/>
									<key>ApRequiresImage4</key>
									<true/>
								</dict>
							</dict>
							<dict>
								<key>Actions</key>
								<dict>
									<key>EPRO</key>
									<false/>
								</dict>
								<key>Conditions</key>
								<dict>
									<key>ApCurrentProductionMode</key>
									<true/>
									<key>ApDemotionPolicyOverride</key>
									<string>Demote</string>
									<key>ApInRomDFU</key>
									<true/>
									<key>ApRequiresImage4</key>
									<true/>
								</dict>
							</dict>
							<dict>
								<key>Actions</key>
								<dict>
									<key>ESEC</key>
									<false/>
								</dict>
								<key>Conditions</key>
								<dict>
									<key>ApRawSecurityMode</key>
									<false/>
									<key>ApRequiresImage4</key>
									<true/>
								</dict>
							</dict>
							<dict>
								<key>Actions</key>
								<dict>
									<key>ESEC</key>
									<true/>
								</dict>
								<key>Conditions</key>
								<dict>
									<key>ApRawSecurityMode</key>
									<true/>
									<key>ApRequiresImage4</key>
									<true/>
								</dict>
							</dict>
						</array>
					</dict>
					<key>Trusted</key>
					<true/>
				</dict>
			</dict>
			<key>ProductMarketingVersion</key>
			<string>26.0</string>
			<key>UniqueBuildID</key>
			<data>
			3dp9XHEcBDbwNi5AQAZbJ+OWIXU=
			</data>
		</dict>
	</array>
	<key>ManifestVersion</key>
	<integer>0</integer>
	<key>ProductBuildVersion</key>
	<string>25A5295e</string>
	<key>ProductVersion</key>
	<string>26.0</string>
	<key>SupportedProductTypes</key>
	<array>
		<string>iSim1,1</string>
	</array>
</dict>
</plist>
