<?xml version="1.0" encoding="utf-8"?>
<pkg-info identifier="com.apple.pkg.InstallAssistant.Seed.macOS26Seed" postinstall-action="none" version="25.0.5295.5" format-version="2" auth="root" generator-version="IASUProductGenerator-27" relocatable="true" useHFSPlusCompression="true" overwrite-permissions="true">
    <payload numberOfFiles="938" installKBytes="34834"/>
    <bundle path="./Applications/Install macOS Tahoe Beta.app" CFBundleShortVersionString="21.0.01" CFBundleVersion="21001" SourceVersion="1882000000000000" id="com.apple.InstallAssistant.Seed.macOS26Seed"/>
    <bundle-version/>
    <upgrade-bundle>
        <bundle id="com.apple.InstallAssistant.Seed.macOS26Seed"/>
    </upgrade-bundle>
    <update-bundle/>
    <atomic-update-bundle/>
    <strict-identifier/>
    <relocate/>
    <scripts>
        <postinstall file="./postinstall.sh" component-id="com.apple.InstallAssistant.Seed.macOS26Seed"/>
    </scripts>
</pkg-info>