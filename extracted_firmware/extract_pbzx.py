#!/usr/bin/env python3
"""
Simple pbzx extractor for macOS installer payloads
"""
import struct
import sys
import lzma
import os

def extract_pbzx(input_file, output_dir):
    """Extract pbzx compressed payload"""
    with open(input_file, 'rb') as f:
        # Read pbzx header
        magic = f.read(4)
        if magic != b'pbzx':
            print(f"Error: Not a pbzx file (magic: {magic})")
            return False
        
        # Skip header fields
        f.read(8)  # Skip 8 bytes

        # Read number of chunks (should be much smaller)
        chunk_data = f.read(8)
        chunk_count = struct.unpack('<Q', chunk_data)[0]  # Try little endian
        if chunk_count > 1000:  # Sanity check
            chunk_count = struct.unpack('>Q', chunk_data)[0]  # Try big endian
        if chunk_count > 1000:  # Still too big, try as 32-bit
            f.seek(-8, 1)  # Go back
            chunk_count = struct.unpack('<I', f.read(4))[0]
            f.read(4)  # Skip padding

        print(f"Found {chunk_count} chunks")
        
        os.makedirs(output_dir, exist_ok=True)
        
        for i in range(chunk_count):
            # Read chunk header
            chunk_header = f.read(24)
            if len(chunk_header) < 24:
                break
                
            # Parse chunk info
            compressed_size = struct.unpack('>Q', chunk_header[8:16])[0]
            uncompressed_size = struct.unpack('>Q', chunk_header[16:24])[0]
            
            print(f"Chunk {i}: compressed={compressed_size}, uncompressed={uncompressed_size}")
            
            # Read compressed data
            compressed_data = f.read(compressed_size)
            if len(compressed_data) != compressed_size:
                print(f"Error: Could not read full chunk {i}")
                break
            
            # Decompress using LZMA
            try:
                decompressed = lzma.decompress(compressed_data)
                
                # Write to output file
                output_file = os.path.join(output_dir, f"chunk_{i:03d}.cpio")
                with open(output_file, 'wb') as out_f:
                    out_f.write(decompressed)
                print(f"Extracted chunk {i} to {output_file}")
                
            except Exception as e:
                print(f"Error decompressing chunk {i}: {e}")
                continue
    
    return True

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python3 extract_pbzx.py <input_pbzx> <output_dir>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_dir = sys.argv[2]
    
    if extract_pbzx(input_file, output_dir):
        print("Extraction completed successfully")
    else:
        print("Extraction failed")
        sys.exit(1)
